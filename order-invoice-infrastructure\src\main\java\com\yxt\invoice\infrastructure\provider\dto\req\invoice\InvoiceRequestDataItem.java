package com.yxt.invoice.infrastructure.provider.dto.req.invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 发票项目类
 */
@Data
public class InvoiceRequestDataItem {

  /**
   * 行号
   */
  @JsonProperty("line")
  private Integer line;

  /**
   * 单品数量
   */
  @JsonProperty("invoiceQty")
  private BigDecimal invoiceQty;

  /**
   * 商品编码
   */
  @JsonProperty("materialId")
  private String materialId;

  /**
   * 商品原价
   */
  @JsonProperty("originalPrice")
  private BigDecimal originalPrice;

  /**
   * 销售单价
   */
  @JsonProperty("realPrice")
  private BigDecimal realPrice;

  /**
   * 总金额
   */
  @JsonProperty("amount")
  private BigDecimal amount;

  /**
   * 兑换损失
   */
  @JsonProperty("loss")
  private Integer loss;

  /**
   * 拆零标识：  非必填
   * ”0”:不拆零
   * “1”-拆零
   */
  @JsonProperty("spbpFlag")
  private Integer spbpFlag;


}