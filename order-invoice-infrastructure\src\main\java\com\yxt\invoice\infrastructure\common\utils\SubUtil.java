package com.yxt.invoice.infrastructure.common.utils;

public class SubUtil {
    
    /**
     * 获取字符串后3位，不足3位时在后面补0
     * @param str 输入字符串
     * @return 处理后的3位字符串
     */
    public static String getLast3WithZeroPadding(String str) {
        if (str == null) {
            return "000";
        }
        
        // 如果字符串长度大于等于3，直接取后3位
        if (str.length() >= 3) {
            return str.substring(str.length() - 3);
        }
        
        // 如果字符串长度小于3，在后面补0
        StringBuilder sb = new StringBuilder(str);
        int zerosToAdd = 3 - str.length();
        for (int i = 0; i < zerosToAdd; i++) {
            sb.append("0");
        }
        
        return sb.toString();
    }
    
    /**
     * 获取字符串后3位，不足3位时在后面补0（使用字符串拼接方式）
     * @param str 输入字符串
     * @return 处理后的3位字符串
     */
    public static String getLast3WithZeroPaddingFormat(String str) {
        if (str == null) {
            return "000";
        }
        
        // 如果字符串长度大于等于3，直接取后3位
        if (str.length() >= 3) {
            return str.substring(str.length() - 3);
        }
        
        // 如果字符串长度小于3，在后面补0
        return (str + "000").substring(0, 3);
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试用例
        System.out.println("测试结果：");
        System.out.println("'12345' -> '" + getLast3WithZeroPadding("12345") + "'");  // 345
        System.out.println("'123' -> '" + getLast3WithZeroPadding("123") + "'");      // 123
        System.out.println("'12' -> '" + getLast3WithZeroPadding("12") + "'");        // 120
        System.out.println("'1' -> '" + getLast3WithZeroPadding("1") + "'");          // 100
        System.out.println("'' -> '" + getLast3WithZeroPadding("") + "'");            // 000
        System.out.println("null -> '" + getLast3WithZeroPadding(null) + "'");        // 000
        System.out.println("'abcdef' -> '" + getLast3WithZeroPadding("abcdef") + "'"); // def
        System.out.println("'ab' -> '" + getLast3WithZeroPadding("ab") + "'");        // ab0
    }
}