package com.yxt.invoice.infrastructure.provider.dto.req.invoice;

import com.yxt.invoice.infrastructure.provider.dto.req.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

// 请求DTO

@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceDetailRequest extends BaseReq {

  /**
   * 蓝票受理业务流水号 与outRequestCode 二选一必填
   */
  private String responseId;
  /**
   * 外部业务请求号 与responseId 二选一必填
   */
  private String outRequestCode;
  /**
   * 0：蓝票 1：红票
   */
  private String invoiceTag;


}