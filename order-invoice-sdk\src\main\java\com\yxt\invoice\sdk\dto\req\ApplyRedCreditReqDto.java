package com.yxt.invoice.sdk.dto.req;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 发票红冲请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class ApplyRedCreditReqDto  implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 开票单号
     */
    @NotEmpty(message = "发票信息不能为空")
    @Valid
    @ApiModelProperty(value = "开票单号", required = true, example = "INV20250811001")
    private String invoiceMainNo;


    /**
     * 申请开票时间
     */
    @NotEmpty(message = "发票信息不能为空")
    @Valid
    @ApiModelProperty(value = "申请开票时间", required = true, example = "2025-08-11 14:30:00")
    private Date applyTime;


    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    @ApiModelProperty(value = "红冲原因 INVOICE_ERROR-开票有误 GOODS_RETURN-销货退回 SERVICE_TERMINATION-服务终止 SALES_ALLOWANCE-销售折让")

    private String redCreditReason;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "开票有误，需要重新开具")
    private String notes;

    /**
     * 操作人用户ID
     */
    @ApiModelProperty(value = "操作人用户ID", example = "USER123456")
    private String operatorUserId;



    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", example = "ORDER20250811001")
    private String orderNo;


}
