package com.yxt.invoice.infrastructure.common.configration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Remote 配置属性类
 */
@Data
@Configuration
@EnableConfigurationProperties(RemoteProperties.class)
@ConfigurationProperties(prefix = "remote")
public class RemoteProperties {

  private Pos pos = new Pos();
  private TaxCloud taxCloud = new TaxCloud();

  /**
   * POS 配置内部类
   */
  @Data
  public static class Pos {
    private String url;
  }

  /**
   * Tax Cloud 配置内部类
   */
  @Data
  public static class TaxCloud {
    private String url;
    private String pSecret;
    private String pId;
  }
}