package com.yxt.lang.exception;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 异常配置属性类
 * 由于yxt-common-lang新版本中删除了此类，但yxt-core-spring-boot-starter:4.6.4仍然引用它
 * 因此在这里重新创建以保持兼容性
 */
@Component
@ConfigurationProperties(prefix = "exception")
public class ExceptionProperties {
    
    private boolean enabled = true;
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
