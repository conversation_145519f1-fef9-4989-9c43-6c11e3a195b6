package com.yxt.invoice.infrastructure.message;

import com.yxt.common.ddd.domain.producer.BaseDomainEventProducer;
import com.yxt.invoice.domain.event.BaseInvoiceDomainEvent;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 10:04
 * @email: <EMAIL>
 */
@Slf4j
@Service
public class InvoiceDomainEventProducer extends BaseDomainEventProducer<BaseInvoiceDomainEvent<?>> {

  @Resource
  private ApplicationEventPublisher applicationEventPublisher;


  @Override
  public void sendDomainEvent(BaseInvoiceDomainEvent domainEvent) {
    applicationEventPublisher.publishEvent(domainEvent);
  }

  public void sendDomainEvents(List<BaseInvoiceDomainEvent<?>> domainEvents) {
    for (BaseInvoiceDomainEvent<?> domainEvent : domainEvents) {
      sendDomainEvent(domainEvent);
    }
  }


}
