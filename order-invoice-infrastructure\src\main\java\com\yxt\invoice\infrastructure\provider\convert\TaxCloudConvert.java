package com.yxt.invoice.infrastructure.provider.convert;

import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.utils.OrderDateUtils;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceRequestData;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceRequestDataItem;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.XinYunOrder;
import com.yxt.invoice.infrastructure.provider.dto.req.red_invoice.RedInvoiceRequestData;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceShowBuyerBankAccountEnum;
import com.yxt.order.types.invoice.remote.RemoteInvoiceCategory;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.logging.log4j.util.Strings;


public class TaxCloudConvert {

  public static InvoiceRequestData buildInvoiceRequest(InvoiceAggregate aggregate) {
    InvoiceMain invoiceMain = aggregate.getInvoiceMain();
    List<InvoiceDetail> invoiceDetailList = aggregate.getInvoiceDetailList();
    String thirdOrderNo = aggregate.getThirdOrderNo();

    InvoiceRequestData invoiceData = getInvoiceData(invoiceMain, invoiceDetailList);
    XinYunOrder xinYunOrder = new XinYunOrder();
    xinYunOrder.setThirdOrderNo(thirdOrderNo);
    invoiceData.setXinYunOrder(xinYunOrder);
    return invoiceData;
  }

  private static InvoiceRequestData getInvoiceData(InvoiceMain invoiceMain,
      List<InvoiceDetail> invoiceDetailList) {
    InvoiceRequestData reqDto = new InvoiceRequestData();
    reqDto.setOutRequestCode(invoiceMain.getInvoiceMainNo());
    reqDto.setInTaxRateTag("Y"); // 是否含税 Y:是 N:否
    reqDto.setInvoiceType(
        RemoteInvoiceCategory.mappingInvoiceType(invoiceMain.getInvoiceType()).getCode());
    reqDto.setBuyerType(
        invoiceMain.getBuyerPartyType().equals(InvoiceBuyerPartyTypeEnum.INDIVIDUAL) ? "Y" : "N");
    reqDto.setBuyerName(invoiceMain.getBuyerName());
//    reqDto.setBuyerTin(invoiceMain.getBuyerTin());
    reqDto.setBuyerTin(Strings.EMPTY); // 税务云让传空
    reqDto.setBuyerAddress(invoiceMain.getBuyerAddress());
    reqDto.setBuyerPhone(invoiceMain.getBuyerPhone());
    reqDto.setBuyerMobile(invoiceMain.getBuyerMobile());
    reqDto.setBuyerBank(invoiceMain.getBuyerBank());
    reqDto.setBuyerBankAccount(invoiceMain.getBuyerBankAccount());
    reqDto.setSellerNumber(invoiceMain.getSellerNumber());
    reqDto.setShowBuyerBankAccount(
        InvoiceShowBuyerBankAccountEnum.SHOW.name().equals(invoiceMain.getShowBuyerBankAccount())
            ? "Y" : "N");
    reqDto.setOperator(invoiceMain.getOperator());
    reqDto.setRequestDate(OrderDateUtils.formatYYMMDD(invoiceMain.getApplyTime()));
    reqDto.setNotes("");
    reqDto.setPayee(invoiceMain.getPayee());
    reqDto.setReviewedBy(invoiceMain.getReviewed());
    reqDto.setSpecificElements("");
//    reqDto.setSplitItem(invoiceMain.getSplitBill());

    List<InvoiceRequestDataItem> invoiceItems = convertToInvoiceItemList(invoiceDetailList);
    reqDto.setItemList(invoiceItems);

    return reqDto;
  }

  private static List<InvoiceRequestDataItem> convertToInvoiceItemList(
      List<InvoiceDetail> invoiceDetailList) {
    return invoiceDetailList.stream().map(TaxCloudConvert::convertToInvoiceItem)
        .collect(Collectors.toList());
  }

  private static InvoiceRequestDataItem convertToInvoiceItem(InvoiceDetail invoiceDetail) {
    InvoiceRequestDataItem item = new InvoiceRequestDataItem();
    item.setLine(Integer.valueOf(invoiceDetail.getLine()));
    item.setInvoiceQty(invoiceDetail.getCommodityCount());
    item.setMaterialId(invoiceDetail.getErpCode());
    item.setOriginalPrice(invoiceDetail.getPrice());
    item.setRealPrice(invoiceDetail.getPrice());
    item.setAmount(invoiceDetail.getTotalAmount());
    item.setLoss(0);
//        item.setSpbpFlag(); // 非必填
    return item;
  }


  public static RedInvoiceRequestData buildRedRequest(InvoiceAggregate redInvoiceAggregate) {
    InvoiceMain invoiceMain = redInvoiceAggregate.getInvoiceMain();
    RedInvoiceRequestData reqDto = new RedInvoiceRequestData();
    reqDto.setSellerNumber(invoiceMain.getSellerNumber()); //
    reqDto.setOutRequestCode(invoiceMain.getInvoiceMainNo());
    reqDto.setOriginalOutRequestCode(invoiceMain.getRedInvoiceMainNo());
    reqDto.setWriteOffReason(invoiceMain.getRedInvoiceReason().getCode());
    reqDto.setIsEntire("Y");
    return reqDto;

  }


}
