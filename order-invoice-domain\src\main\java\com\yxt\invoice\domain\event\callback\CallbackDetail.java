package com.yxt.invoice.domain.event.callback;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

@Data
public class CallbackDetail {

  private String outRequestCode;

  private String invoiceTag;

  private String pId;

  private String channel;

  private String responseId;

  private String invoiceStatus;

  private String uploadStatus;

  private String statusMsg;

  private String resetInvoice;

  private String invoicePdf;

  private String downPdf;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date issueDate;

  private String invoiceCode;

  private String requestCode;

  /**
   * 纳税人识别号
   */
  private String sellerTin;


  private Long invoiceId;
}
