package com.yxt.invoice.interfaces.mq.es.es_invoice_main;


import static com.yxt.invoice.interfaces.config.KafkaConfig.CANAL_GROUP_ID_INVOICE_ES;
import static com.yxt.invoice.interfaces.config.ThreadPoolConfig.SCHEDULED_THREAD_POOL;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.model.EsInvoiceMainModel;
import com.yxt.lang.util.JsonUtils;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class EsInvoiceMainMessageConsumer {
  @Qualifier(SCHEDULED_THREAD_POOL)
  @Resource
  private ScheduledExecutorService scheduledThreadPool;


  @Resource
  private List<AbstractCanalHandler<? extends BaseCanalData<?>, EsInvoiceMainModel>> abstractCanalHandlerList;


  @KafkaListener(topics = {"${canal.invoice}"},
      groupId = CANAL_GROUP_ID_INVOICE_ES, containerFactory = "canalKafkaListenerFactory",concurrency = "1")
  public void invoiceEsMessage(List<ConsumerRecord<String, String>> consumerRecordList,
      Acknowledgment ack) {
    try {

      if (CollectionUtils.isEmpty(consumerRecordList)) {
        return;
      }

      List<String> messageList = consumerRecordList.stream()
          .filter(s -> !StringUtils.isEmpty(s.value()))
          .filter(s -> !JsonUtils.toObject(s.value(), new TypeReference<BaseCanalData<?>>() {})
              .getIsDdl())
          .map(ConsumerRecord::value)
          .collect(Collectors.toList());

      if (CollectionUtils.isEmpty(messageList)) {
        return;
      }

      for (String message : messageList) {
        try {
          for (AbstractCanalHandler<? extends BaseCanalData<?>, ? extends BaseEsIndexModel> abstractCanalHandler : abstractCanalHandlerList) {
            if (abstractCanalHandler.execBusiness(message)) {
              break;
            }
          }
        } catch (Exception e) {
          log.warn("支持发票业务,同步到ES异常,入延迟处理线程池再次处理,当前异常原因(忽略):{},message: {},",
              e.getMessage(), message);
          scheduledThreadPool.schedule(() -> delayHandle(message), 10, TimeUnit.SECONDS);
        }
      }

      ack.acknowledge();
    } catch (Exception e) {
      log.error("invoiceEsMessage error,cause:{},data:{}", e.getMessage(),
          consumerRecordList, e);
    }
  }

  /**
   * 延迟处理
   *
   * @param message
   */
  private void delayHandle(String message) {
    try {
      for (AbstractCanalHandler<? extends BaseCanalData<?>, ? extends BaseEsIndexModel> abstractCanalHandler : abstractCanalHandlerList) {
        if (abstractCanalHandler.execBusiness(message)) {
          break;
        }
      }
    } catch (Exception e) {
      log.error("支持发票业务,同步到ES异常后延迟处理依然异常,原因:{},message: {}", e.getMessage(),
          message);
    }
  }

}