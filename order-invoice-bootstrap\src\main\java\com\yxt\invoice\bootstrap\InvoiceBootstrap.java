package com.yxt.invoice.bootstrap;

import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.starter.register.EsMapperScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;


@SpringBootApplication
@EnableFeignClients(basePackages = {"com.yxt.invoice","com.yxt.permission"})
@ComponentScan(basePackages = {"com.yxt.invoice","com.yxt.order","com.yxt.permission"})
@Slf4j
@EsMapperScan("com.yxt.invoice.infrastructure.db.es")
@MapperScan("com.yxt.invoice.infrastructure.db.mysql")
@EnableAsync
@EnableAspectJAutoProxy
public class InvoiceBootstrap {

  public static void main(String[] args) {
    SpringApplication.run(InvoiceBootstrap.class, args);
    log.info("order-invoice-service 服务启动成功");
  }
}