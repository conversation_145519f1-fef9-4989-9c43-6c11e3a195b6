package com.yxt.invoice.infrastructure.db.mysql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 发票主表DO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
@TableName("invoice_log_api")
public class InvoiceLogApiDO {

  /**
   * 主键ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 开票单号
   */
  private String invoiceMainNo;

  /**
   * 操作员Id
   */
  private String operatorId;

  /**
   * 操作员
   */
  private String operatorName;


  /**
   * 操作描述
   */
  private String content;

  /**
   * 日志类型
   */
  private String logType;


  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 位置
   */
  private String position;

}
