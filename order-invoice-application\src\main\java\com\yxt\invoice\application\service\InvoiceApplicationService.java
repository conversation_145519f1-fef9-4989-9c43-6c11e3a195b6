package com.yxt.invoice.application.service;

import com.yxt.invoice.domain.command.*;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.lang.dto.api.PageDTO;

/**
 * 发票应用服务接口
 * Application层不依赖SDK，使用Domain对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceApplicationService {



    /**
     * 申请开票
     *
     * @param command 开票申请命令
     * @return 发票聚合根
     */
    String applyInvoice(ApplyInvoiceCommand command);
    /**
     * 发票红冲
     *
     * @param command 红冲命令
     * @return 发票聚合根
     */
    String applyRedInvoice(RedCreditInvoiceCommand command);



    /**
     * 查询发票列表
     *
     * @param query 列表查询
     * @return 发票列表
     */
    PageDTO<InvoiceMain> pageInvoiceList(QueryInvoiceListCommand query);

    /**
     * 查询发票详情
     *
     * @param query 详情查询
     * @return 发票聚合根
     */
    InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query);



    ExistsOrderInvoice queryOrderExistsInvoice(OrderExistsInvoiceQueryCommand command);




    ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(ExistsThirdOrderInvoiceCommand command);
}
