package com.yxt.invoice.infrastructure.provider.feign;


import com.yxt.invoice.infrastructure.common.api_log.ApiLog;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceDetailRequest;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceRequest;
import com.yxt.invoice.infrastructure.provider.dto.req.red_invoice.RedInvoiceRequest;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.invoice.InvoiceDetailResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.red_invoice.RedInvoiceDetailResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.invoice.InvoiceResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.red_invoice.RedInvoiceResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 税务云接口
 *
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@FeignClient(name = "tax-cloud-service", url = "${remote.tax-cloud.url}")
public interface TaxCloudFeign {

  @PostMapping("/positiveInvoiceBatch")
  @ApiLog(value = "税务云·申请蓝票", businessNo = "#req.data[0].outRequestCode")
  InvoiceResponse applyInvoice(@RequestBody InvoiceRequest req);

  @PostMapping("/postNegativeInvoiceIssue")
  @ApiLog(value = "税务云·申请红票", businessNo = "#req.data.outRequestCode")
  RedInvoiceResponse applyRedInvoice(@RequestBody RedInvoiceRequest req);

  @PostMapping("/getInvoiceByOutRequestCode")
  @ApiLog(value = "税务云·查询蓝票详情", businessNo = "#req.outRequestCode")
  InvoiceDetailResponse queryInvoice(@RequestBody InvoiceDetailRequest req);

  /**
   * 独立一个查红票的接口。接口地址还是一样的
   */
  @PostMapping("/getInvoiceByOutRequestCode")
  @ApiLog(value = "税务云·查询红票详情", businessNo = "#req.outRequestCode")
  RedInvoiceDetailResponse queryRedInvoice(@RequestBody InvoiceDetailRequest req);

}
