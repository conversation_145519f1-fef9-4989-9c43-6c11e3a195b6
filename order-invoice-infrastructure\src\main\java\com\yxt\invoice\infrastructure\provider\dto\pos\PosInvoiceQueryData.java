package com.yxt.invoice.infrastructure.provider.dto.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

// 响应DTO
@Data
public class PosInvoiceQueryData implements Serializable {

  private String invoiceId; // 发票单号: POS中的发票单据号

  @JsonProperty("responseid") // 发票业务流水号
  private String responseId; // 发票响应号: 由税务云返回的发票响应号

  private List<String> salenoList; // 销售单号列表 根据销售单号关联到的销售单号列表，包含其本身

  private String invoiceStatus;
}
