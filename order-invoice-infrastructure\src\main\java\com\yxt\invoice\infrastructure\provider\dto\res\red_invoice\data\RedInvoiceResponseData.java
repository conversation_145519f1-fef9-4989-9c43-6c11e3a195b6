package com.yxt.invoice.infrastructure.provider.dto.res.red_invoice.data;

import com.yxt.order.types.invoice.remote.RemoteInvoiceTag;
import lombok.Data;

@Data
public class RedInvoiceResponseData {

  /**
   * 发票号码
   */
  private String invoiceCode;

  /**
   * 发票标识 0，蓝票，1 红票
   * @see RemoteInvoiceTag
   */
  private String invoiceTag;

  /**
   * 通道
   */
  private String channel;

  /**
   * 通道名称
   */
  private String channelName;

  /**
   * 业务提交门店编码
   */
  private String realitySellerNumber;


  /**
   * 蓝字发票号码
   */
  private String positiveInvoiceNo;

  /**
   * 业务流水号
   */
  private String responseId;

  /**
   * 平台 ID
   */
  private String pId;

  /**
   * 发票上传状态 00：上传成功 01：发票上传处理中 02：上传失败 03：重复上传 99：待上传
   * @see com.yxt.order.types.invoice.remote.RemoteInvoiceUploadStatus
   */
  private String uploadStatus;

  /**
   * invoiceStatus 01：蓝票申请 02：红冲申请 03：蓝票申请失败 04：红票发票失败 05：号码已分配 06：蓝票开具成功 07：红票开具成功 08：红字确认单录入中
   * @see com.yxt.order.types.invoice.remote.RemoteInvoiceStatus
   */
  private String invoiceStatus;

  /************************************************************************
   *仅红字发票返回
   */

  /**
   * 红冲状态： 0 待处理， 1 处理中， 2 处理完成, 3 错误
   * @see com.yxt.order.types.invoice.remote.RemoteWriteOffState
   */
  private String writeOffState;

  /**
   * 红字冲销金额
   */
  private String hzcxje;

  /**
   * 红字冲销税额
   */
  private String hzcxse;

  /**
   * 红字确认单状态代码 00:未确认 01:确认中 02:确认失败 03:确认完成
   * @see com.yxt.order.types.invoice.remote.RemoteRedConfirmationStatus
   */
  private String hzqrxxztDm;

  /**
   * 红字发票 uuid
   */
  private String uuid;

  /**
   * 红字确认单编号
   */
  private String hzfpxxqrdbh;

  /**
   * 冲红原因: 01：开票有误 02：销货退回 03：服务终止 04：销售折让
   * @see com.yxt.order.types.invoice.remote.RemoteRedInvoiceReason
   */
  private String chyyDm;

  /**
   * 状态描述
   */
  private String statusMsg;


  /**
   * ********************************
   */
  private String receiveData;

}