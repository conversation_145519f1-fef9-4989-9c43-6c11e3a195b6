package com.yxt.invoice.sdk.dto.req;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QueryThirdOrderExistsInvoiceReqDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 第三方平台订单号
     */
    @ApiModelProperty("三方平台订单号")
    private String thirdOrderNo;


}
