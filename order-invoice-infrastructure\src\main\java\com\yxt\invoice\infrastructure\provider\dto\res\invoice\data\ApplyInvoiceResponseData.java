package com.yxt.invoice.infrastructure.provider.dto.res.invoice.data;

import com.yxt.order.types.invoice.remote.RemoteInvoiceTag;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 申请发票响应
 */
@Data
public class ApplyInvoiceResponseData {

  /**
   * 发票号码
   */
  private String invoiceCode;

  /**
   * 发票标识 0，蓝票，1 红票
   *
   * @see RemoteInvoiceTag
   */
  private String invoiceTag;

  /**
   * 通道
   */
  private String channel;

  /**
   * 通道名称
   */
  private String channelName;


  /**
   * 业务提交门店编码
   */
  private String realitySellerNumber;


  /**
   * 业务流水号
   */
  private String responseId;

  /**
   * 平台 ID
   */
  private String pId;

  /**
   * 发票上传状态 00：上传成功 01：发票上传处理中 02：上传失败 03：重复上传 99：待上传
   *
   * @see com.yxt.order.types.invoice.remote.RemoteInvoiceUploadStatus
   */
  private String uploadStatus;

  /**
   * invoiceStatus 01：蓝票申请 02：红冲申请 03：蓝票申请失败 04：红票发票失败 05：号码已分配 06：蓝票开具成功 07：红票开具成功 08：红字确认单录入中
   *
   * @see com.yxt.order.types.invoice.remote.RemoteInvoiceStatus
   */
  private String invoiceStatus;

  /**
   * 价税合计 仅蓝票返回
   */
  private BigDecimal priceTaxAmount;

  /**
   * 发票金额
   */
  private BigDecimal invoiceAmount;

  /**
   * 税额
   */
  private BigDecimal taxAmount;

  /**
   * 状态描述
   */
  private String statusMsg;

  /**
   * 接收数据
   */
  private String receiveData;

}