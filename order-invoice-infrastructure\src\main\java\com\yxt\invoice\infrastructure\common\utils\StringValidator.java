package com.yxt.invoice.infrastructure.common.utils;

public class StringValidator {

  /**
   * 判断字符串是否全为英文字符（可以包含空格）
   *
   * @param str 待判断的字符串
   * @return true表示全英文（可含空格），false表示不是
   */
  public static boolean isAllEnglish(String str) {
    // 空字符串或null返回false
    if (str == null || str.isEmpty()) {
      return false;
    }

    // 纯空格字符串返回false
    if (str.trim().isEmpty()) {
      return false;
    }

    // 使用正则表达式：只允许英文字母(a-z, A-Z)和空格
    return str.matches("[a-zA-Z ]+");
  }

  /**
   * 判断字符串是否全为中文字符（不可以包含空格）
   *
   * @param str 待判断的字符串
   * @return true表示全中文（不含空格），false表示不是
   */
  public static boolean isAllChinese(String str) {
    // 空字符串或null返回false
    if (str == null || str.isEmpty()) {
      return false;
    }

    // 使用正则表达式：只允许中文字符（Unicode范围 \u4e00-\u9fff）
    return str.matches("[\\u4e00-\\u9fff]+");
  }

  /**
   * 智能判断字符串类型并进行相应校验 根据第一个字符判断是英文还是中文，然后应用对应的校验规则 注意：第一个字符不能是空格
   *
   * @param str 待判断的字符串
   * @return true表示符合对应语言的规则，false表示不符合
   */
  public static boolean isValidString(String str) {
    // 空字符串或null返回false
    if (str == null || str.isEmpty()) {
      return false;
    }

    // 获取第一个字符
    char firstChar = str.charAt(0);

    // 第一个字符不能是空格
    if (firstChar == ' ') {
      return false;
    }

    // 判断第一个字符的类型
    if (isEnglishChar(firstChar)) {
      // 如果是英文字符，使用英文校验逻辑
      return isAllEnglish(str);
    } else if (isChineseChar(firstChar)) {
      // 如果是中文字符，使用中文校验逻辑
      return isAllChinese(str);
    } else {
      // 既不是英文也不是中文，返回false
      return false;
    }
  }

  /**
   * 判断字符是否为英文字符
   *
   * @param c 待判断的字符
   * @return true表示是英文字符，false表示不是
   */
  private static boolean isEnglishChar(char c) {
    return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z');
  }

  /**
   * 判断字符是否为中文字符
   *
   * @param c 待判断的字符
   * @return true表示是中文字符，false表示不是
   */
  private static boolean isChineseChar(char c) {
    return c >= '\u4e00' && c <= '\u9fff';
  }

  // 测试方法
  public static void main(String[] args) {
    System.out.println("\n=== 智能判断测试 ===");
    System.out.println("'Hello World' -> " + isValidString("Hello World")); // true（英文逻辑）
    System.out.println("'  Hello World' -> " + isValidString("  Hello World")); // false（第一个字符是空格）
    System.out.println("'你好世界' -> " + isValidString("你好世界"));           // true（中文逻辑）
    System.out.println("'  你好世界' -> " + isValidString("  你好世界"));       // false（第一个字符是空格）
    System.out.println("'你好 世界' -> " + isValidString("你好 世界"));         // false（中文逻辑，不允许空格）
    System.out.println("'Hello123' -> " + isValidString("Hello123"));       // false（英文逻辑，包含数字）
    System.out.println("'你好123' -> " + isValidString("你好123"));           // false（中文逻辑，包含数字）
    System.out.println("'123Hello' -> " + isValidString("123Hello"));       // false（首字符是数字）
    System.out.println("' Hello' -> " + isValidString(" Hello"));           // false（第一个字符是空格）
    System.out.println("'   ' -> " + isValidString("   "));                 // false（第一个字符是空格）
    System.out.println("'' -> " + isValidString(""));                       // false（空字符串）
  }
}