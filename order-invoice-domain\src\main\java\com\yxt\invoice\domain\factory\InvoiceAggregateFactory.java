package com.yxt.invoice.domain.factory;

import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.external.IdService;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.order.types.invoice.enums.InvoiceDataSourceEnum;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 发票工厂
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Component
public class InvoiceAggregateFactory {


  @Resource
  private IdService idService;

  public InvoiceAggregate createAggravate(ApplyInvoiceCommand command) {
    // 生成内部单号
    String invoiceMainNo = idService.generateId(IdService.IdType.INVOICE_MAIN_NO);

    InvoiceMain invoiceMain = command.getInvoiceMain();
    invoiceMain.setInvoiceMainNo(invoiceMainNo);
    invoiceMain.setDataSource(InvoiceDataSourceEnum.INVOICE.name());

    command.getDetails().forEach(d -> {
      d.setInvoiceMainNo(invoiceMainNo);
      d.setInvoiceDetailNo(idService.generateId(IdService.IdType.INVOICE_MAIN_DETAIL_NO));
    });

    String thirdOrderNo = command.getThirdOrderNo();
    InvoiceAggregate build = InvoiceAggregate.build(invoiceMain, command.getDetails());
    build.setThirdOrderNo(thirdOrderNo);
    return build;
  }

  public InvoiceAggregate redAggregate(InvoiceAggregate aggregate,
      RedCreditInvoiceCommand command) {
    InvoiceAggregate redInvoiceAggregate = aggregate.generateRedAggregate();
    redInvoiceAggregate.applyRed(command);
    return redInvoiceAggregate;

  }
}
