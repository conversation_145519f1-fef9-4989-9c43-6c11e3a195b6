package com.yxt.invoice.infrastructure.common.external;

import com.yxt.invoice.domain.external.IdService;
import com.yxt.invoice.infrastructure.common.feign.MiddleIdClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月03日 13:51
 * @email: <EMAIL>
 */
@Component
public class IdServiceImpl implements IdService {

  @Resource
  private MiddleIdClient middleIdClient;

  @Override
  public String generateId(IdType idType) {
    Long id = middleIdClient.getId(1).get(0);
    switch (idType) {
      case INVOICE_MAIN_NO:
      case INVOICE_MAIN_DETAIL_NO:
      case RED_INVOICE_MAIN_NO:
      case RED_INVOICE_MAIN_DETAIL_NO:
        return String.valueOf(id);
      default:
        throw new RuntimeException(String.format("暂不支持未知的IdType生成Id,[%s]", idType));
    }
  }


}
