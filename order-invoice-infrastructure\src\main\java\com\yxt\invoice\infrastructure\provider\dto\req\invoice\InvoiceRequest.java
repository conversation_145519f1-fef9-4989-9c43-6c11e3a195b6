package com.yxt.invoice.infrastructure.provider.dto.req.invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.invoice.infrastructure.provider.dto.req.BaseReq;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * 主请求类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceRequest extends BaseReq {

  @JsonProperty("data")
  private List<InvoiceRequestData> data;

  public void valid() {
    Assert.isTrue(!CollectionUtils.isEmpty(data), "data集合必须传值");
    for (InvoiceRequestData item : data) {
      item.valid();
    }
  }

}

