//package com.yxt.invoice.application.command;
//
//import java.math.BigDecimal;
//
///**
// * 发票明细命令
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025-08-06
// */
//public class InvoiceDetailCommand {
//
//    /**
//     * 商品编码
//     */
//    private String erpCode;
//
//    /**
//     * 商品名称
//     */
//    private String erpName;
//
//    /**
//     * 商品数量
//     */
//    private BigDecimal commodityCount;
//
//    /**
//     * 商品规格
//     */
//    private String commoditySpec;
//
//    /**
//     * 单位
//     */
//    private String unit;
//
//    /**
//     * 商品售价
//     */
//    private BigDecimal price;
//
//    /**
//     * 税率
//     */
//    private BigDecimal taxRate;
//
//    /**
//     * 税收分类编码
//     */
//    private String taxClassificationCode;
//
//    /**
//     * 私有构造函数
//     */
//    private InvoiceDetailCommand() {
//    }
//
//    /**
//     * 建造者模式
//     */
//    public static Builder builder() {
//        return new Builder();
//    }
//
//    public static class Builder {
//        private InvoiceDetailCommand command = new InvoiceDetailCommand();
//
//        public Builder erpCode(String erpCode) {
//            command.erpCode = erpCode;
//            return this;
//        }
//
//        public Builder erpName(String erpName) {
//            command.erpName = erpName;
//            return this;
//        }
//
//        public Builder commodityCount(BigDecimal commodityCount) {
//            command.commodityCount = commodityCount;
//            return this;
//        }
//
//        public Builder commoditySpec(String commoditySpec) {
//            command.commoditySpec = commoditySpec;
//            return this;
//        }
//
//        public Builder unit(String unit) {
//            command.unit = unit;
//            return this;
//        }
//
//        public Builder price(BigDecimal price) {
//            command.price = price;
//            return this;
//        }
//
//        public Builder taxRate(BigDecimal taxRate) {
//            command.taxRate = taxRate;
//            return this;
//        }
//
//        public Builder taxClassificationCode(String taxClassificationCode) {
//            command.taxClassificationCode = taxClassificationCode;
//            return this;
//        }
//
//        public InvoiceDetailCommand build() {
//            return command;
//        }
//    }
//
//    // Getters
//    public String getErpCode() {
//        return erpCode;
//    }
//
//    public String getErpName() {
//        return erpName;
//    }
//
//    public BigDecimal getCommodityCount() {
//        return commodityCount;
//    }
//
//    public String getCommoditySpec() {
//        return commoditySpec;
//    }
//
//    public String getUnit() {
//        return unit;
//    }
//
//    public BigDecimal getPrice() {
//        return price;
//    }
//
//    public BigDecimal getTaxRate() {
//        return taxRate;
//    }
//
//    public String getTaxClassificationCode() {
//        return taxClassificationCode;
//    }
//}
