package com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler.data.CanalInvoice.Invoice;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:06
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalInvoice extends BaseCanalData<Invoice> {

  @Data
  public static class Invoice {

    @JsonProperty("invoice_main_no")
    private String invoiceMainNo;

    /**
     * 会员编号
     */
    @JsonProperty("user_id")
    private String userId;

    /**
     * 分公司编码
     */
    @JsonProperty("company_code")
    private String companyCode;


    /**
     * 机构编码
     */
    @JsonProperty("organization_code")
    private String organizationCode;


    /**
     * 第三方平台编码
     */
    @JsonProperty("third_platform_code")
    private String thirdPlatformCode;


    /**
     * 第三方订单号
     */
    @JsonProperty("third_order_no")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * pos销售单号
     */
    @JsonProperty("pos_no")
    private String posNo;

    /**
     * 红蓝字标识
     */
    @JsonProperty("invoice_red_blue_type")
    private String invoiceRedBlueType;

    /**
     * 发票状态
     */
    @JsonProperty("invoice_status")
    private String invoiceStatus;

    /**
     * 购方类型
     */
    @JsonProperty("buyer_party_type")
    private String buyerPartyType;

    /**
     * 同步状态
     */
    @JsonProperty("sync_status")
    private String syncStatus;

    /**
     * 是否有效
     */
    @JsonProperty("is_valid")
    private Long isValid;

    /**
     * 申请时间
     */
    @JsonProperty("apply_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

  }
}

