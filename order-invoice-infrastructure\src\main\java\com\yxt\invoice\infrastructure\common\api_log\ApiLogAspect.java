package com.yxt.invoice.infrastructure.common.api_log;

import static com.yxt.order.types.invoice.enums.InvoiceLogTypeApiEnum.API_REQUEST;
import static com.yxt.order.types.invoice.enums.InvoiceLogTypeApiEnum.API_RESPONSE;

import com.yxt.invoice.infrastructure.common.utils.LogUtils;
import com.yxt.lang.util.JsonUtils;
import java.lang.reflect.Method;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class ApiLogAspect {


  @Around("@annotation(apiLog)")
  public Object around(ProceedingJoinPoint pjp, ApiLog apiLog) throws Throwable {
    // 获取方法信息
    MethodSignature signature = (MethodSignature) pjp.getSignature();
    Method method = signature.getMethod();

    // 获取调用位置
    String position = getCallerPosition();

    // 获取业务单号
    String businessNo = getBusinessNo(pjp, method, apiLog.businessNo());

    // 请求参数JSON
    String requestJson = JsonUtils.toJson(pjp.getArgs());

    // 记录请求日志
    LogUtils.logApi(businessNo, requestJson, API_REQUEST.name(), position);

    Object result;
    try {
      // 执行目标方法
      result = pjp.proceed();

      // 响应结果JSON
      String responseJson = JsonUtils.toJson(result);

      // 记录响应日志
      LogUtils.logApi(businessNo, responseJson, API_RESPONSE.name(), position);

    } catch (Exception e) {
      // 记录异常响应日志
      String errorJson =
          "{\"error\":\"" + e.getMessage() + "\",\"exception\":\"" + e.getClass().getSimpleName()
              + "\"}";
      LogUtils.logApi(businessNo, errorJson, API_RESPONSE.name(), position);
      throw e;
    }

    return result;
  }
  /**
   * 获取业务单号
   */
  private String getBusinessNo(ProceedingJoinPoint pjp, Method method, String businessNoSpel) {
    if (businessNoSpel == null || businessNoSpel.trim().isEmpty()) {
      return "";
    }

    try {
      ExpressionParser parser = new SpelExpressionParser();
      LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
      String[] params = discoverer.getParameterNames(method);
      Object[] args = pjp.getArgs();

      EvaluationContext context = new StandardEvaluationContext();

      // 设置方法参数到上下文
      if (params != null && args != null) {
        for (int i = 0; i < params.length && i < args.length; i++) {
          context.setVariable(params[i], args[i]);
        }
      }

      Expression expression = parser.parseExpression(businessNoSpel);
      Object value = expression.getValue(context);
      return value != null ? value.toString() : "";

    } catch (Exception e) {
      // 日志记录SpEL表达式解析失败，但不影响主要业务流程
      System.err.println("解析businessNo SpEL表达式失败: " + e.getMessage());
      return "";
    }
  }

  /**
   * 获取调用者位置信息 通过堆栈跟踪找到A类调用B类log方法的具体位置
   */
  private String getCallerPosition() {
    try {
      StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();

      // 遍历堆栈，寻找真正的调用者
      for (StackTraceElement element : stackTrace) {
        String className = element.getClassName();

        // 跳过当前切面类、Spring代理类、反射调用等
        if (!className.contains("ApiLogAspect") && !className.contains("$Proxy")
            && !className.contains("CGLIB") && !className.startsWith("java.lang.reflect")
            && !className.startsWith("org.springframework") && !className.startsWith("sun.reflect")
            && !className.startsWith("jdk.internal.reflect")) {

          return String.format("%s.%s(%s:%d)", className, element.getMethodName(),
              element.getFileName(), element.getLineNumber());
        }
      }

      // 如果没有找到合适的调用者，返回第一个非系统类
      for (StackTraceElement element : stackTrace) {
        if (!element.getClassName().startsWith("java.") && !element.getClassName()
            .startsWith("sun.") && !element.getClassName().startsWith("jdk.")
            && !element.getClassName().contains("ApiLogAspect")) {
          return String.format("%s.%s(%s:%d)", element.getClassName(), element.getMethodName(),
              element.getFileName(), element.getLineNumber());
        }
      }

    } catch (Exception e) {
      return "获取调用位置失败: " + e.getMessage();
    }

    return "未知位置";
  }
}