package com.yxt.invoice.bootstrap;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yxt.invoice.infrastructure.provider.dto.res.invoice.InvoiceResponse;
import com.yxt.lang.util.JsonUtils;
import org.junit.Test;

public class JsonTest {

  @Test
  public void testParse(){
    String json = "{\n"
        + "    \"code\": 0,\n"
        + "    \"msg\": \"业务受理成功\",\n"
        + "    \"responseId\": null,\n"
        + "    \"data\": [\n"
        + "        {\n"
        + "            \"outRequestCode\": \"1841680736480937990\",\n"
        + "            \"channel\": \"07\",\n"
        + "            \"channelName\": \"压力测试\",\n"
        + "            \"realitySellerNumber\": \"1000\",\n"
        + "            \"invoiceTag\": \"0\",\n"
        + "            \"invoiceStatus\": \"03\",\n"
        + "            \"uploadStatus\": \"02\",\n"
        + "            \"statusMsg\": \"ERROR:开票人不能为空\",\n"
        + "            \"taxAmount\": 1.02,\n"
        + "            \"invoiceAmount\": 8,\n"
        + "            \"priceTaxAmount\": 9.02,\n"
        + "            \"mqkey\": \"invoice_backwrite_timeout_key\",\n"
        + "            \"invoiceId\": 0,\n"
        + "            \"pid\": \"YXT00010\",\n"
        + "            \"psecret\": null\n"
        + "        }\n"
        + "    ],\n"
        + "    \"receiveData\": \"{\\r\\n    \\\"data\\\": [\\r\\n        {\\r\\n            \\\"outRequestCode\\\": \\\"1841680736480937990\\\",\\r\\n            \\\"inTaxRateTag\\\": \\\"Y\\\",\\r\\n            \\\"invoiceType\\\": \\\"01\\\",\\r\\n            \\\"buyerType\\\": \\\"Y\\\",\\r\\n            \\\"buyerName\\\": \\\"buyerName\\\",\\r\\n            \\\"buyerTin\\\": \\\"341125199911112565\\\",\\r\\n            \\\"buyerMobile\\\": \\\"***********\\\",\\r\\n            \\\"buyerAddress\\\": \\\"buyerAddress\\\",\\r\\n            \\\"buyerPhone\\\": \\\"***********\\\",\\r\\n            \\\"sellerNumber\\\": \\\"1000\\\",\\r\\n            \\\"showBuyerBankAccount\\\": \\\"SHOW\\\",\\r\\n            \\\"buyerBank\\\": \\\"buyerBank\\\",\\r\\n            \\\"buyerBankAccount\\\": \\\"****************\\\",\\r\\n            \\\"payee\\\": null,\\r\\n            \\\"notes\\\": \\\"\\\",\\r\\n            \\\"splitItem\\\": \\\"NOT\\\",\\r\\n            \\\"requestDate\\\": \\\"2025-08-26 15:14:00\\\",\\r\\n            \\\"operator\\\": null,\\r\\n            \\\"reviewedBy\\\": null,\\r\\n            \\\"specificElements\\\": \\\"\\\",\\r\\n            \\\"itemList\\\": [\\r\\n                {\\r\\n                    \\\"line\\\": 166,\\r\\n                    \\\"invoiceQty\\\": 1.0,\\r\\n                    \\\"materialId\\\": \\\"191055\\\",\\r\\n                    \\\"originalPrice\\\": 8.9,\\r\\n                    \\\"realPrice\\\": 8.9,\\r\\n                    \\\"amount\\\": 8.9,\\r\\n                    \\\"loss\\\": 0,\\r\\n                    \\\"spbpFlag\\\": null\\r\\n                }\\r\\n            ]\\r\\n        }\\r\\n    ],\\r\\n    \\\"pId\\\": \\\"YXT00010\\\",\\r\\n    \\\"pSecret\\\": \\\"Wsn5ZKoe5ihWf+aGQ7AQBSdBQZw==\\\"\\r\\n}\"\n"
        + "}";

    InvoiceResponse object = JsonUtils.toObject(json, new TypeReference<InvoiceResponse>() {
    });
    System.out.println();
  }

}
