package com.yxt.invoice.sdk.dto;

import com.yxt.order.types.invoice.enums.InvoiceAmountKeyEnum;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;


@Data
public class InvoiceAmountDTO {

    @ApiModelProperty(value = "类型 INVOICE_AMOUNT-用户实付金额 INVOICE_AMOUNT_WITH_POST_FEE-用户实付金额/运费 INVOICE_AMOUNT_WITH_POST_FEE_WITH_SUBSIDY-用户实付金额+运费+补贴 INVOICE_AMOUNT_WITH_SUBSIDY-用户实付金额+补贴")
    private InvoiceAmountKeyEnum key;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal amount;
}
