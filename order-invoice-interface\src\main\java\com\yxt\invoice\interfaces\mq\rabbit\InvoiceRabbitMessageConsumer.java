package com.yxt.invoice.interfaces.mq.rabbit;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.rabbitmq.client.Channel;
import com.yxt.invoice.domain.event.callback.CallbackInvoiceDetail;
import com.yxt.invoice.domain.event.callback.CallbackRedInvoiceDetail;
import com.yxt.invoice.domain.event.callback.InvoiceBaseMsgModel;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties.TaxCloud;
import com.yxt.invoice.infrastructure.common.utils.LogUtils;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceDetailRequest;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.invoice.InvoiceDetailResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.invoice.InvoiceDetailResponseData;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.red_invoice.RedInvoiceDetailResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.red_invoice.RedInvoiceDetailResponseData;
import com.yxt.invoice.infrastructure.provider.feign.TaxCloudFeign;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.invoice.enums.InvoiceLogTypeApiEnum;
import com.yxt.order.types.invoice.remote.RemoteInvoiceTag;
import java.io.IOException;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 发票相关RabbitMQ消息监听器
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Component
@Slf4j
public class InvoiceRabbitMessageConsumer {

  @Resource
  private InvoiceRepository invoiceRepository;

  @Resource
  private TaxCloudFeign taxCloudFeign;

  @Resource
  private RemoteProperties remoteProperties;


  /**
   * 处理流程: MQ通知 --> 详情 --> 基于详情处理结果
   *
   * @throws IOException
   */
  @RabbitListener(queues = "${rabbit.consumer.tax-cloud-topic}", containerFactory = "multiRabbitListenerContainerFactory")
  @RabbitHandler
  public void receiveBroadcastB2C(Message message, Channel channel) throws IOException {

    try {
      String msg = new String(message.getBody());
      log.info("发表回调通知:{}", msg);

      // 记录到日志表
      InvoiceBaseMsgModel invoiceBaseMsgModel = fetchBaseAndLog(msg);

      TaxCloud taxCloud = remoteProperties.getTaxCloud();

      // 调用详情接口
      InvoiceDetailRequest req = new InvoiceDetailRequest();
      req.setPId(taxCloud.getPId());
      req.setPSecret(taxCloud.getPSecret());
      req.setResponseId(invoiceBaseMsgModel.getResponseId());
      String outRequestCode = invoiceBaseMsgModel.getOutRequestCode();
      req.setOutRequestCode(outRequestCode);
      req.setInvoiceTag(invoiceBaseMsgModel.getInvoiceTag());

      if (RemoteInvoiceTag.BLUE_INVOICE.getCode().equals(invoiceBaseMsgModel.getInvoiceTag())) {
        handlerInvoice(req);
      } else if (RemoteInvoiceTag.RED_INVOICE.getCode()
          .equals(invoiceBaseMsgModel.getInvoiceTag())) {
        handlerRedInvoice(req);
      } else {
        log.error("暂不支持该业务场景:{}", msg);
        throw new RuntimeException("暂不支持该业务场景");
      }

      //ACK,确认一条消息已经被消费
      channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    } catch (Exception e) {
      //NACK basicNack(deliveryTag, multiple, requeue)
      channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
      throw new RuntimeException(e);
    }
  }

  private void handlerInvoice(InvoiceDetailRequest req) {
    String invoiceMainNo = req.getOutRequestCode();
    InvoiceDetailResponse res = taxCloudFeign.queryInvoice(req);
    if(!res.success()){
      log.error("mq回调查询详情接口失败,{}",JsonUtils.toJson(req));
      return;
    }

    InvoiceDetailResponseData data = res.getData();
    CallbackInvoiceDetail callbackInvoiceDetail = BeanUtil.toBean(data,CallbackInvoiceDetail.class);
    InvoiceAggregate invoiceAggregate = invoiceRepository.findByInvoiceMainNo(invoiceMainNo);
    invoiceAggregate.callBack(callbackInvoiceDetail);
    invoiceRepository.doSave(invoiceAggregate);


  }

  private void handlerRedInvoice(InvoiceDetailRequest req) {
    String invoiceMainNo = req.getOutRequestCode();
    RedInvoiceDetailResponse res = taxCloudFeign.queryRedInvoice(req);
    RedInvoiceDetailResponseData data = res.getData();
    CallbackRedInvoiceDetail redInvoiceMsgModel = BeanUtil.toBean(data,CallbackRedInvoiceDetail.class);
    InvoiceAggregate redAggregate = invoiceRepository.findByInvoiceMainNo(invoiceMainNo);
    redAggregate.redCallBack(redInvoiceMsgModel);
    invoiceRepository.doSave(redAggregate);
  }

  private InvoiceBaseMsgModel fetchBaseAndLog(String msg) {
    try {
      InvoiceBaseMsgModel baseMsgModel = JsonUtils.toObject(msg,
          new TypeReference<InvoiceBaseMsgModel>() {
          });
      String outRequestCode = baseMsgModel.getOutRequestCode(); //我们系统内部订单号
      LogUtils.logApi(outRequestCode, msg, InvoiceLogTypeApiEnum.MQ.name(), getPosition());
      return baseMsgModel;
    } catch (Exception e) {
      LogUtils.logApi("InvoiceBaseMsgModel parse error", msg, InvoiceLogTypeApiEnum.MQ.name(),
          getPosition());
      throw new RuntimeException("转换异常记录原始消息");
    }

  }

  @NotNull
  private String getPosition() {
    return this.getClass().getName() + ":tid:" + Thread.currentThread().getId();
  }
}
