package com.yxt.invoice.infrastructure.provider.dto.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Data;

// 响应DTO
@Data
public class PosInvoiceQueryReq implements Serializable {

    /**
     * 三方单号
     */
    @JsonProperty("saleno")
    private String thirdOrderNo;

    /**
     * 门店编码
     */
    @JsonProperty("mdm_busno")
    private String storeCode;


}
