package com.yxt.invoice.infrastructure.provider.dto.res.invoice;

import com.yxt.invoice.infrastructure.provider.dto.res.BaseRes;
import com.yxt.invoice.infrastructure.provider.dto.res.invoice.data.ApplyInvoiceResponseData;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

// 响应DTO
@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceResponse extends BaseRes {


  /**
   * 发票数据
   */
  private List<ApplyInvoiceResponseData> data;

}