package com.yxt.invoice.application.third.goods.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年01月30日 9:47
 * @email: <EMAIL>
 */

@Data
public class AveragePriceQuery {
    @ApiModelProperty("当前页，从第1页开始，不传默认为1")
    private int currentPage = 1;
    @ApiModelProperty("每页显示条数，不传默认20")
    private int pageSize = 20;

    /**
     * 商户编码 不能为空
     */
    private String merCode;

    /**
     * 角色 默认1
     */
    private Integer admin;

    /**
     * 门店编码 不能为空
     */
    private String storeCode;

    /**
     * 商品erp编码 不能为空
     */
    private List<String> erpCodes;


    private String storeId;
    private String wareHouseCode;

    private List<String> specIds;


    public static AveragePriceQuery buildBean(String merCode, String storeCode, List<String> erpCodeList) {
        AveragePriceQuery queryCommodityStockDto = new AveragePriceQuery();
        queryCommodityStockDto.setAdmin(1);
        queryCommodityStockDto.setMerCode(merCode);
        queryCommodityStockDto.setStoreCode(storeCode);
        queryCommodityStockDto.setErpCodes(erpCodeList);
        queryCommodityStockDto.setCurrentPage(1);
        queryCommodityStockDto.setPageSize(erpCodeList.size());
        return queryCommodityStockDto;
    }


}
