package com.yxt.invoice.sdk.dto.req;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 发票明细请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class InvoiceDetailReqDto  implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 开票单号
     */
    @ApiModelProperty(value = "开票单号", required = true, example = "INV20250811001")
    private String invoiceMainNo;




}
