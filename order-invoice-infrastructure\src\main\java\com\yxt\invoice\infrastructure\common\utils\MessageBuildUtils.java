package com.yxt.invoice.infrastructure.common.utils;

import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import java.util.List;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

public class MessageBuildUtils {

  public static String buildInvoiceStatus(List<InvoiceAggregate> invoiceAggregateList){
    if(CollectionUtils.isEmpty(invoiceAggregateList)){
      return Strings.EMPTY;
    }

    StringBuilder sb = new StringBuilder();

    for (InvoiceAggregate invoiceAggregate : invoiceAggregateList) {
      InvoiceMain invoiceMain = invoiceAggregate.getInvoiceMain();
      String orderNo = invoiceMain.getOrderNo().getOrderNo();
      InvoiceStatusEnum invoiceStatus = invoiceMain.getInvoiceStatus();

      StringBuilder contentBuilder = new StringBuilder();
      contentBuilder.append(String.format("订单号: %s,状态: %s ",orderNo,invoiceStatus.getDesc()));
      if(InvoiceStatusEnum.FAIL.equals(invoiceStatus) || InvoiceStatusEnum.RED_FAIL.equals(invoiceStatus)){
        String invoiceErrMsg = invoiceMain.getInvoiceErrMsg();
        contentBuilder.append(String.format("失败原因:%s",invoiceErrMsg));
      }

      contentBuilder.append(";");
      sb.append(contentBuilder);
    }
    return sb.toString();
  }

}
