package com.yxt.invoice.interfaces.converter;

import com.google.common.base.Preconditions;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.ExistsThirdOrderInvoiceCommand;
import com.yxt.invoice.domain.command.QueryInvoiceDetailCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.invoice.infrastructure.common.UserContext;
import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.invoice.sdk.dto.req.ApplyInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.req.ApplyRedInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.req.InvoiceDetailReqDto;
import com.yxt.invoice.sdk.dto.req.QueryOrderExistsInvoiceReqDto;
import com.yxt.invoice.sdk.dto.req.QueryThirdOrderExistsInvoiceReqDto;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import com.yxt.order.types.offline.OfflineOrderNo;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * 发票请求转换器 负责将SDK请求对象转换为Domain Command或Application Query
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
public class InvoiceRequestConverter {


  /**
   * 转换发票详情请求为查询对象
   */
  public static QueryInvoiceDetailCommand convertToInvoiceDetailCommand(
      InvoiceDetailReqDto request) {
    log.debug("开始转换发票详情请求为查询对象");

    if (request == null) {
      throw new IllegalArgumentException("详情查询请求不能为空");
    }
    QueryInvoiceDetailCommand command = new QueryInvoiceDetailCommand();
    command.setInvoiceMainNo(request.getInvoiceMainNo());

    return command;
  }


  public static ApplyInvoiceCommand createInvoiceCommand(ApplyInvoiceMainReqDto reqDto) {
    ApplyInvoiceCommand command = new ApplyInvoiceCommand();
    command.setInvoiceMain(buildInvoiceMain(reqDto));
    command.setInvoiceAmount(convertToInvoiceAmount(reqDto.getInvoiceAmount()));
    String userId = UserContext.getCurrentUserId();
    if (Objects.isNull(userId)) {
      throw new RuntimeException("请登录");
    }
    log.info("userId:{} operatorUserId:{}", userId, reqDto.getOperatorUserId());
    Preconditions.checkArgument(userId.equals(reqDto.getOperatorUserId()),
        "当前用户ID与操作用户ID不一致");
    return command;
  }


  public static InvoiceMain buildInvoiceMain(ApplyInvoiceMainReqDto reqDto) {
    InvoiceMain invoiceMain = new InvoiceMain();
    invoiceMain.setOrderNo(OfflineOrderNo.orderNo(reqDto.getOrderNo()));
    invoiceMain.setMerCode(reqDto.getMerCode());
    invoiceMain.setBusinessType(reqDto.getBusinessType());
    invoiceMain.setTransactionChannel(reqDto.getTransactionChannel());
    invoiceMain.setCreatedBy(reqDto.getOperatorUserId());
    invoiceMain.setUpdatedBy(reqDto.getOperatorUserId());
    invoiceMain.setUserId(reqDto.getUserId());
    invoiceMain.setInvoiceType(InvoiceTypeEnum.fromCode(reqDto.getInvoiceType()));
    invoiceMain.setNotes(reqDto.getNotes());
    invoiceMain.setApplyTime(reqDto.getApplyTime());
    invoiceMain.setApplyChannel(reqDto.getApplyChannel());
    invoiceMain.setBuyerPartyType(InvoiceBuyerPartyTypeEnum.fromCode(reqDto.getBuyerPartyType()));
    invoiceMain.setBuyerName(reqDto.getBuyerName());
    invoiceMain.setBuyerTin(reqDto.getBuyerTin());
    invoiceMain.setBuyerAddress(reqDto.getBuyerAddress());
    invoiceMain.setBuyerPhone(reqDto.getBuyerPhone());
    invoiceMain.setBuyerBank(reqDto.getBuyerBank());
    invoiceMain.setBuyerBankAccount(reqDto.getBuyerBankAccount());
    invoiceMain.setBuyerEmail(reqDto.getBuyerEmail());
    invoiceMain.setBuyerMobile(reqDto.getBuyerMobile());
    invoiceMain.setShowBuyerBankAccount(reqDto.getShowBuyerBankAccount());
    invoiceMain.setInvoiceRedBlueType(InvoiceRedBlueTypeEnum.TAX_INVOICE);
    return invoiceMain;
  }


  private static InvoiceAmount convertToInvoiceAmount(InvoiceAmountDTO reqDto) {
    InvoiceAmount invoiceAmount = new InvoiceAmount();
    invoiceAmount.setKey(reqDto.getKey());
    invoiceAmount.setAmount(reqDto.getAmount());
    return invoiceAmount;
  }

  public static RedCreditInvoiceCommand createRedInvoiceCommand(ApplyRedInvoiceMainReqDto reqDto) {
    RedCreditInvoiceCommand command = new RedCreditInvoiceCommand();
    command.setInvoiceMainNo(reqDto.getTargetInvoiceMainNo());
    command.setRedCreditReason(reqDto.getRedInvoiceReason());
    command.setNotes(reqDto.getNotes());
    command.setOperatorUserId(reqDto.getOperatorUserId());
    return command;

  }

  public static OrderExistsInvoiceQueryCommand convertToExistsOrderInvoiceCommand(
      QueryOrderExistsInvoiceReqDto reqDto) {
    OrderExistsInvoiceQueryCommand command = new OrderExistsInvoiceQueryCommand();
    command.setOrderNo(reqDto.getOrderNo());
    command.setTransactionChannel(reqDto.getTransactionChannel());
    command.setBusinessType(reqDto.getBusinessType());
    command.setPosNo(reqDto.getPosNo());
    return command;
  }

  public static ExistsThirdOrderInvoiceCommand convertToExistsThirdOrderInvoiceCommand(
      QueryThirdOrderExistsInvoiceReqDto reqDto) {
    ExistsThirdOrderInvoiceCommand command = new ExistsThirdOrderInvoiceCommand();
    command.setThirdOrderNo(reqDto.getThirdOrderNo());
    return command;
  }


}
