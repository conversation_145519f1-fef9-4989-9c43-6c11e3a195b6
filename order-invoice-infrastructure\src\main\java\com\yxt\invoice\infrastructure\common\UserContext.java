package com.yxt.invoice.infrastructure.common;

import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户上下文工具类
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class UserContext {

    /**
     * 用户ID请求头名称
     */
    private static final String USER_ID_HEADER = "X-User-Id";

    /**
     * 用户名请求头名称
     */
    private static final String USER_NAME_HEADER = "X-User-Name";

    /**
     * 获取当前用户ID
     */
    public static String getCurrentUserId() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        
        String userId = request.getHeader(USER_ID_HEADER);
        if (!StringUtils.hasText(userId)) {
            // 从参数中获取
            userId = request.getParameter("userId");
        }
        
        return userId;
    }

    /**
     * 获取当前用户名
     */
    public static String getCurrentUserName() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        
        return request.getHeader(USER_NAME_HEADER);
    }

    /**
     * 验证用户权限
     */
    public static void validateUserPermission(String targetUserId) {
        String currentUserId = getCurrentUserId();
        if (!StringUtils.hasText(currentUserId)) {
            throw new RuntimeException("用户未登录");
        }
        
        if (!currentUserId.equals(targetUserId)) {
            throw new RuntimeException("无权限访问其他用户的数据");
        }
    }

    /**
     * 获取当前请求
     */
    private static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
