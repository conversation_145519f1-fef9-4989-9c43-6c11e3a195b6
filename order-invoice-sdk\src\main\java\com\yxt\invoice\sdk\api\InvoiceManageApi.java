package com.yxt.invoice.sdk.api;

import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.invoice.sdk.dto.req.InvoiceDetailReqDto;
import com.yxt.invoice.sdk.dto.req.InvoiceListReqDto;
import com.yxt.invoice.sdk.dto.res.InvoiceDetailResponse;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 发票服务SDK接口 为医药电商平台提供开票相关服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceManageApi {

  /**
   * 查询发票列表
   *
   * @param request 发票列表查询请求
   * @return 发票列表结果
   */
  @PostMapping("/list")
  @ApiOperation(value = "管理后台-发票列表")
  ResponseBase<PageDTO<InvoiceMainDTO>> invoiceList(@Valid @RequestBody InvoiceListReqDto request);

  /**
   * 查询发票详情
   *
   * @param req 发票ID
   * @return 发票详情结果
   */
  @PostMapping("/detail")
  @ApiOperation(value = "管理后台-发票详情")
  ResponseBase<InvoiceDetailResponse> detail(@Valid @RequestBody InvoiceDetailReqDto req);

}
