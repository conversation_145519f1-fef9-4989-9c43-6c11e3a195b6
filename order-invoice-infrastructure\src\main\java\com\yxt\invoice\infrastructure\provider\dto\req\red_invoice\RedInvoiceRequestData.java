package com.yxt.invoice.infrastructure.provider.dto.req.red_invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;
import lombok.Data;
import org.springframework.util.Assert;

/**
 * 红票数据类
 */
@Data
public class RedInvoiceRequestData {

  /**
   * 销方编码
   */
  @JsonProperty("sellerNumber")
  private String sellerNumber;

  /**
   * 外部请求号
   */
  @JsonProperty("outRequestCode")
  private String outRequestCode;

  /**
   * 原蓝字发票请求号
   */
  @JsonProperty("originalOutRequestCode")
  private String originalOutRequestCode;

  /**
   * 冲红原因 01：开票有误 02：销货退回 03：服务中止 04：销售折让 门店通常选择”01”、”02” 服务类发票选择“03”
   */
  @JsonProperty("writeOffReason")
  private String writeOffReason;

  /**
   * 是否整单红冲 Y:是 N：否
   */
  @JsonProperty("isEntire")
  private String isEntire;


  public void valid(){
  }

}
