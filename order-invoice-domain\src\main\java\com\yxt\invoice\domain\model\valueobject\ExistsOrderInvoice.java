package com.yxt.invoice.domain.model.valueobject;

import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class ExistsOrderInvoice {

    /**
     * 平台编码
     */
    @ApiModelProperty("平台编码")
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @ApiModelProperty("三方平台订单号")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * pos销售单号
     */
    @ApiModelProperty("pos销售单号")
    private String posNo;

    /**
     * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
     */
    @ApiModelProperty("交易场景 ONLINE:线上交易, OFFLINE:线下交易")
    private String transactionChannel;

    /**
     * 业务类型  O2O、B2C、B2B、OFFLINE
     */
    @ApiModelProperty("业务类型  O2O、B2C、B2B、OFFLINE")
    private String businessType;

    /**
     * 是否已开票
     */
    @ApiModelProperty("发票信息 已申请时有值")
    private List<InvoiceMain> invoiceMains;

    @ApiModelProperty("发票开具金额选项 未申请时有值")
    private List<InvoiceAmount> invoiceAmounts;



    public static ExistsOrderInvoice getHasExistsOrderInvoice( List<InvoiceAggregate> invoiceAggregateList ) {
        InvoiceAggregate invoiceAggregate = invoiceAggregateList.get(0);
        InvoiceMain invoiceMain = invoiceAggregate.getInvoiceMain();
        List<InvoiceMain> collect = invoiceAggregateList.stream().map(InvoiceAggregate::getInvoiceMain).collect(Collectors.toList());

        ExistsOrderInvoice existsOrderInvoice = new ExistsOrderInvoice() ;
        existsOrderInvoice.setThirdPlatformCode(invoiceMain.getThirdPlatformCode());
        existsOrderInvoice.setThirdOrderNo(invoiceMain.getThirdOrderNo().getThirdOrderNo());
        existsOrderInvoice.setOrderNo(invoiceMain.getOrderNo().getOrderNo());
        existsOrderInvoice.setPosNo(invoiceMain.getPosNo());
        existsOrderInvoice.setTransactionChannel(invoiceMain.getTransactionChannel());
        existsOrderInvoice.setBusinessType(invoiceMain.getBusinessType());
        existsOrderInvoice.setInvoiceMains(collect);

        return existsOrderInvoice;
    }




}
