package com.yxt.invoice.infrastructure.event;

import com.yxt.invoice.domain.event.create.PullBackInvoiceInfoEvent;
import com.yxt.invoice.domain.event.create.PullBackInvoiceInfoEvent.Data;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.repository.RemoteDataResult.RemoteDetail;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties.TaxCloud;
import com.yxt.invoice.infrastructure.common.utils.LogUtils;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceDetailRequest;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.invoice.InvoiceDetailResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.invoice.InvoiceDetailResponseData;
import com.yxt.invoice.infrastructure.provider.feign.TaxCloudFeign;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.invoice.enums.InvoiceDataSourceEnum;
import com.yxt.order.types.invoice.enums.InvoiceIsValidEnum;
import com.yxt.order.types.invoice.enums.InvoiceLogTypeApiEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;
import com.yxt.order.types.invoice.remote.RemoteInvoiceStatus;
import com.yxt.order.types.invoice.remote.RemoteInvoiceTag;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PullBackInvoiceInfoEventHandler implements EventHandler {

  @Resource
  private InvoiceRepository invoiceRepository;


  @Resource
  private TaxCloudFeign taxCloudFeign;

  @Resource
  private RemoteProperties remoteProperties;

  @Override
  public void handle(PullBackInvoiceInfoEvent event) {

    Data data = event.getData();
    List<RemoteDetail> remoteDetailList = data.getRemoteDetailList();

    for (RemoteDetail remoteDetail : remoteDetailList) {
      saveInvoiceMain(remoteDetail, data.getInvoiceAggregate());
    }
  }


  private void saveInvoiceMain(RemoteDetail remoteDetail, InvoiceAggregate invoiceAggregate) {
    InvoiceMain invoiceMain = invoiceAggregate.getInvoiceMain();
    String responseId = remoteDetail.getResponseId();
    RemoteInvoiceStatus invoiceStatus = remoteDetail.getInvoiceStatus();

    TaxCloud taxCloud = remoteProperties.getTaxCloud();
    InvoiceDetailRequest reqDto = new InvoiceDetailRequest();
    reqDto.setResponseId(responseId);
    reqDto.setOutRequestCode(Strings.EMPTY);
    if (RemoteInvoiceStatus.blueSuccess(invoiceStatus)) {
      reqDto.setInvoiceTag(RemoteInvoiceTag.BLUE_INVOICE.getCode());
    } else if (RemoteInvoiceStatus.redSuccess(invoiceStatus)) {
      reqDto.setInvoiceTag(RemoteInvoiceTag.RED_INVOICE.getCode());
    } else {
      log.error("系统只处理蓝票成功和红票成功的数据,其他状态的数据不处理");
      return;
    }
    reqDto.setPId(taxCloud.getPId());
    reqDto.setPSecret(taxCloud.getPSecret());
    InvoiceDetailResponse taxCloudResponse = taxCloudFeign.queryInvoice(reqDto);
    if (!taxCloudResponse.success()) {
      log.warn("taxCloudFeign.getInvoiceByOutRequestCode 接口调用失败请求参数:{} 响应参数:{}",
          JsonUtils.toJson(reqDto), JsonUtils.toJson(taxCloudResponse));
      return;
    }
    InvoiceDetailResponseData taxCloudData = taxCloudResponse.getData();

    invoiceMain.cleanSellerAndBuyerInfo();
    invoiceMain.setRemoteInvoiceCode(taxCloudData.getInvoiceCode());
    invoiceMain.setRemoteInvoiceResponseId(taxCloudData.getResponseId()); // 业务流水号

    InvoiceRedBlueTypeEnum invoiceRedBlueTypeEnum;
    // invoiceTag 发票标识 0 蓝票，1 红票
    String invoiceTag = taxCloudData.getInvoiceTag();
    if (RemoteInvoiceTag.BLUE_INVOICE.getCode().equals(invoiceTag)) {
      invoiceRedBlueTypeEnum = InvoiceRedBlueTypeEnum.TAX_INVOICE;
    } else if (RemoteInvoiceTag.RED_INVOICE.getCode().equals(invoiceTag)) {
      invoiceRedBlueTypeEnum = InvoiceRedBlueTypeEnum.CREDIT_NOTE;
    } else {
      log.error("系统暂不支持: invoiceTag: {}", invoiceTag);
      return;
    }

    invoiceMain.setInvoiceRedBlueType(invoiceRedBlueTypeEnum);
    String invoiceCloudStatus = taxCloudData.getInvoiceStatus();
    invoiceMain.setInvoiceStatus(InvoiceStatusEnum.mappingToInvoiceStatus(invoiceCloudStatus));
    invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.DONE); // 从平台查出来的,默认已经回传成功
    invoiceMain.setPdfUrl(taxCloudData.getDownPdf());
    invoiceMain.setIsValid(InvoiceIsValidEnum.VALID.getCode());
    Date issueDate = taxCloudData.getIssueDate();
    invoiceMain.setApplyTime(issueDate); // 使用完成时间当做申请开票时间
    invoiceMain.setCompleteTime(issueDate);
    invoiceMain.setCreated(issueDate);
    invoiceMain.setUpdated(issueDate);
    invoiceMain.setSysCreateTime(new Date());
    invoiceMain.setSysUpdateTime(new Date());
    invoiceMain.setDataSource(InvoiceDataSourceEnum.REMOTE.name());

    // 开票主体还是购方信息不用处理
    // todo notey 明细在联调时再看实际返回的,按照实际处理
    InvoiceAggregate rebuild = InvoiceAggregate.build(invoiceMain, null);
    invoiceRepository.doSave(rebuild);
  }

  @NotNull
  private String getPosition() {
    return this.getClass().getName() + ":tId:" + Thread.currentThread().getId();
  }


}
