package com.yxt.invoice.interfaces.service.impl;

import com.yxt.invoice.application.service.InvoiceApplicationService;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.ExistsThirdOrderInvoiceCommand;
import com.yxt.invoice.domain.command.QueryInvoiceDetailCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.lang.dto.api.PageDTO;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发票服务实现 Service层，使用Command传参，委托给Application层处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {


  @Resource
  private InvoiceApplicationService invoiceApplicationService;


  @Override
  public String applyInvoice(ApplyInvoiceCommand command) {
    command.validateApplyInvoice();
    return invoiceApplicationService.applyInvoice(command);
  }

  @Override
  public String applyRedInvoice(RedCreditInvoiceCommand command) {
    return invoiceApplicationService.applyRedInvoice(command);
  }

  @Override
  public ExistsOrderInvoice queryOrderExistsInvoice(OrderExistsInvoiceQueryCommand command) {

    return invoiceApplicationService.queryOrderExistsInvoice(command);
  }

  @Override
  public ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(
      ExistsThirdOrderInvoiceCommand command) {
    return invoiceApplicationService.queryThirdOrderExistsInvoiceReqDto(command);
  }

  @Override
  public PageDTO<InvoiceMain> pageInvoiceList(QueryInvoiceListCommand query) {
    return invoiceApplicationService.pageInvoiceList(query);
  }

  @Override
  public InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query) {
    return invoiceApplicationService.queryInvoiceDetail(query);
  }


}
