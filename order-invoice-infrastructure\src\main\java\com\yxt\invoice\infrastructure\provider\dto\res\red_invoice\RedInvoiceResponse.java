package com.yxt.invoice.infrastructure.provider.dto.res.red_invoice;

import com.yxt.invoice.infrastructure.provider.dto.res.BaseRes;
import com.yxt.invoice.infrastructure.provider.dto.res.red_invoice.data.RedInvoiceResponseData;
import lombok.Data;
import lombok.EqualsAndHashCode;

// 响应DTO note: 和蓝票结构是一样的,只是为了区分复制了一份，独立维护
@EqualsAndHashCode(callSuper = true)
@Data
public class RedInvoiceResponse extends BaseRes {

  /**
   * 发票数据
   */
  private RedInvoiceResponseData data;


}