//package com.yxt.invoice.bootstrap;
//
//
//import com.yxt.domain.order.order_query.req.B2cOrderPageSearchReq;
//import com.yxt.domain.order.order_query.req.B2cOrderSearchByOrderNoReq;
//import com.yxt.domain.order.order_query.res.B2cOmsOrderInfoResDto;
//import com.yxt.domain.order.order_query.res.B2cOrderListResDto;
//import com.yxt.domain.order.refund_query.req.B2cRefundPageSearchReq;
//import com.yxt.domain.order.refund_query.req.B2cRefundSearchByRefundNoReq;
//import com.yxt.domain.order.refund_query.res.B2cRefundPageSearchRes;
//import com.yxt.domain.order.refund_query.res.B2cRefundSearchByRefundNoRes;
//import com.yxt.invoice.application.third.order.feign.B2COrderQueryDomainApiFeign;
//import com.yxt.invoice.application.third.order.feign.B2CRefundQueryDomainApiFeign;
//import com.yxt.invoice.application.third.order.feign.OrderQueryDomainApiFeign;
//import com.yxt.invoice.application.third.order.feign.RefundQueryDomainApiFeign;
//import com.yxt.lang.dto.api.PageDTO;
//import com.yxt.lang.dto.api.ResponseBase;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//
//@Slf4j
//public class B2cInterfaceTest extends BaseTest {
//
//
//  @Resource
//  private B2COrderQueryDomainApiFeign b2COrderQueryDomainApiFeign;
//
//  @Resource
//  private B2CRefundQueryDomainApiFeign b2CRefundQueryDomainApiFeign;
//
//
//  @Resource
//  private OrderQueryDomainApiFeign orderQueryDomainApiFeign;
//
//  @Resource
//  private RefundQueryDomainApiFeign refundQueryDomainApiFeign;
//
//
//  /**
//   * pass
//   */
//  @Test
//  public void testOrderSearchPage() {
//    jRetry(() -> {
//      B2cOrderPageSearchReq req = new B2cOrderPageSearchReq();
//      req.setCurrentPage(1L);
//      req.setPageSize(100L);
//      req.setOrderNo("1780873544180268032");
//      ResponseBase<PageDTO<B2cOrderListResDto>> pageDTOResponseBase = b2COrderQueryDomainApiFeign.orderSearchPage(
//          req);
//
//      System.out.println();
//    });
//  }
//
//  /**
//   * pass
//   */
//  @Test
//  public void testOrderSearchByOrderNo() {
//    jRetry(() -> {
//      B2cOrderSearchByOrderNoReq req = new B2cOrderSearchByOrderNoReq();
//      req.setOmsOrderNo("1780873544180268032");
//      ResponseBase<B2cOmsOrderInfoResDto> b2cOmsOrderInfoResDtoResponseBase = b2COrderQueryDomainApiFeign.orderSearchByOrderNo(
//          req);
//      System.out.println();
//    });
//  }
//
//  /**
//   * pass
//   */
//  @Test
//  public void testRefundSearchPage() {
//    jRetry(() -> {
//      B2cRefundPageSearchReq req = new B2cRefundPageSearchReq();
//      req.setCurrentPage(1L);
//      req.setPageSize(100L);
//      req.setRefundNo("1840237022154351109");
//      ResponseBase<PageDTO<B2cRefundPageSearchRes>> pageDTOResponseBase = b2CRefundQueryDomainApiFeign.refundSearchPage(
//          req);
//      System.out.println();
//    });
//  }
//
//
//  /**
//   * pass
//   */
//  @Test
//  public void testRefundSearchByRefundNo() {
//    jRetry(() -> {
//      B2cRefundSearchByRefundNoReq req = new B2cRefundSearchByRefundNoReq();
//      req.setThirdRefundNo("80155361483");
//      req.setThirdPlatformCode("27");
//
//      ResponseBase<B2cRefundSearchByRefundNoRes> b2cRefundSearchByRefundNoResResponseBase = b2CRefundQueryDomainApiFeign.refundSearchByRefundNo(
//          req);
//      System.out.println();
//    });
//  }
//
//
//  public static void jRetry(JRetryVoidFunction jRetryVoidFunction) {
//    for (; ; ) {
//      try {
//        jRetryVoidFunction.retry();
//        System.out.println();
//      } catch (Exception e) {
//        log.error("exception , {}", e);
//
//      }
//    }
//  }
//
//  public interface JRetryVoidFunction extends JRetryFunction {
//
//    /**
//     * 重试方法
//     */
//    void retry();
//
//  }
//
//  public interface JRetryFunction {
//
//  }
//
//}
