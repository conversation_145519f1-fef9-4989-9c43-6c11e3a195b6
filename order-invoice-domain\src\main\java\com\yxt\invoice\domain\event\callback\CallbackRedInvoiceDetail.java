package com.yxt.invoice.domain.event.callback;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CallbackRedInvoiceDetail extends CallbackDetail {

  /********仅红票返回 start********/

  private String writeOffState;

  /**
   * 红字冲销金额
   */
  private BigDecimal hzcxje;

  /**
   * 红字冲销金额
   */
  private BigDecimal hzcxse;


  /**
   * 红字确认单状态代码 00:未确认 01:确认中 02:确认失败 03:确认完成
   */
  private String hzqrxxztDm;


  private Long uuid;

  /**
   * 红字确认单编号
   */
  private String hzfpxxqrdbh;


  /**
   * 冲红原因: 01：开票有误 02：销货退回 03：服务终止 04：销售折让
   */
  private String chyyDm;


  private String positiveInvoiceNo;

  /********仅红票返回 end********/
}
