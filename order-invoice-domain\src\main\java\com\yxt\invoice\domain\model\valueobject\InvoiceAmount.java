package com.yxt.invoice.domain.model.valueobject;

import com.yxt.order.types.invoice.enums.InvoiceAmountKeyEnum;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class InvoiceAmount {

    @ApiModelProperty("类型  用户实付金额:INVOICE_AMOUNT  用户实付金额/运费:INVOICE_AMOUNT_WITH_POST_FEE  用户实付金额+运费+补贴:INVOICE_AMOUNT_WITH_POST_FEE_WITH_SUBSIDY   用户实付金额+补贴:INVOICE_AMOUNT_WITH_SUBSIDY")
    private InvoiceAmountKeyEnum key;

    @ApiModelProperty("发票金额")
    private BigDecimal amount;

    public InvoiceAmount(InvoiceAmountKeyEnum key, BigDecimal amount) {
        this.key = key;
        this.amount = amount;
    }

    public InvoiceAmount() {

    }

}
