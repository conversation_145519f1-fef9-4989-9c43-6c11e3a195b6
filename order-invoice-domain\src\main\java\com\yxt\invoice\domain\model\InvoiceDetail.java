package com.yxt.invoice.domain.model;

import cn.hutool.extra.spring.SpringUtil;
import com.yxt.invoice.domain.external.IdService;
import com.yxt.invoice.domain.external.IdService.IdType;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;


/**
 * 发票明细实体 根据数据库表 invoice_detail 设计
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class InvoiceDetail {


  /**
   * 明细ID
   */
  private Long id;

  /**
   * 开票单号
   */
  private String invoiceMainNo;

  /**
   * 开票单明细号
   */
  private String invoiceDetailNo;

  /**
   * 行号
   */
  private String rowNo;

  /**
   * 发票行号
   */
  private String line;

  /**
   * 税收分类编码
   */
  private String taxClassificationCode;

  /**
   * 税收分类编码父级分类名称
   */
  private String topLevelTaxClassificationCode;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 商品规格
   */
  private String commoditySpec;

  /**
   * 单位
   */
  private String unit;

  /**
   * 商品售价
   */
  private BigDecimal price;

  /**
   * 商品总额=price*数量
   */
  private BigDecimal totalAmount;

  /**
   * 行税额行金额/(1+税率)*税率
   */
  private BigDecimal taxAmount;

  /**
   * 税率
   */
  private BigDecimal taxRate;

  /**
   * 税率编码
   */
  private String taxRateCode;

  /**
   * 价税合计
   */
  private BigDecimal priceTaxAmount;

  /**
   * 行性质 REGULAR_LINE-正常行 DISCOUNT_LINE-折扣行 DISCOUNTED_LINE-被折扣行
   */
  private String invoiceLineType;

  /**
   * 抵扣金额
   */
  private BigDecimal discountAmount;

  /**
   * 启动优惠政策 YES-启用 NO-不启用
   */
  private String policyStatus;

  /**
   * 优惠标识
   */
  private String policyTag;

  /**
   * 优惠税率
   */
  private BigDecimal policyTaxRate;

  /**
   * 是否起效 1-起效 -1-未起效
   */
  private Long isValid;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  public void redApply(String redInvoiceMainNo) {
    this.invoiceMainNo = redInvoiceMainNo;
    this.invoiceDetailNo = SpringUtil.getBean(IdService.class)
        .generateId(IdType.RED_INVOICE_MAIN_DETAIL_NO);
    this.created = new Date();
    this.updated = new Date();
  }


  public void resetRedInvoice() {
    this.id = null;
    this.invoiceMainNo = Strings.EMPTY;
    this.invoiceDetailNo = Strings.EMPTY;
    this.createdBy = Strings.EMPTY;
    this.updatedBy = Strings.EMPTY;
    this.sysCreateTime = new Date();
    this.sysUpdateTime = new Date();
  }
}
