package com.yxt.invoice.domain.event.callback;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CallbackInvoiceDetail extends CallbackDetail{


  /********仅蓝票返回 start********/
  /**
   * 蓝票税额
   */
  private BigDecimal taxAmount;


  private BigDecimal invoiceAmount;

  private BigDecimal priceTaxAmount;
  /********仅蓝票返回 end********/

}
