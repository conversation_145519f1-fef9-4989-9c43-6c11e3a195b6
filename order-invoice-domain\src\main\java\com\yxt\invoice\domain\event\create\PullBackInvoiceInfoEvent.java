package com.yxt.invoice.domain.event.create;

import com.yxt.invoice.domain.event.BaseCreateInvoiceDomainEvent;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.RemoteDataResult.RemoteDetail;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 拉回发票信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class PullBackInvoiceInfoEvent extends BaseCreateInvoiceDomainEvent<PullBackInvoiceInfoEvent.Data> {

    public static final String TYPE = "PullBackInvoiceInfoEvent";

    public PullBackInvoiceInfoEvent(InvoiceAggregate aggregate,List<RemoteDetail> remoteDetailList) {
        super(aggregate, null, TYPE,new Data(aggregate,remoteDetailList));
    }

    @Getter
    @Setter
    @ToString(callSuper = true)
    @NoArgsConstructor
    public static class Data extends BaseCreateInvoiceDomainEvent.BaseData {
        private InvoiceAggregate invoiceAggregate;
        private List<RemoteDetail> remoteDetailList;

        Data(InvoiceAggregate aggregate,List<RemoteDetail> remoteDetailList) {
            super.convert(aggregate);
            this.invoiceAggregate = aggregate;
            this.remoteDetailList = remoteDetailList;
        }
    }
}
