package com.yxt.invoice.domain.model.valueobject;

import com.yxt.invoice.domain.command.PageDateConditionCommand;
import lombok.Data;

@Data
public class QueryListCondition extends PageDateConditionCommand {
    /**
     * C端用户ID
     */
    private String userId;

    /**
     * 操作人工号/用户ID
     */
    private String operatorId;


    /**
     * 分公司编码
     */
    private String companyCode;


    /**
     * 所属机构编码
     */
    private String organizationCode;


    /**
     * 开票单号
     */
    private String invoiceMainNo;

    /**
     * 平台编码
     */
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    private String thirdOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * pos销售单号
     */
    private String posNo;


    /**
     * 蓝票:TAX_INVOICE 红票:CREDIT_NOTE
     */
    private String invoiceRedBlueType;

    /**
     * 发票状态
     */
    private String invoiceStatus;


    /**
     * 买方类型
     */
    private String buyerPartyType;

    /**
     * 同步状态
     */
    private String syncStatus;


}
