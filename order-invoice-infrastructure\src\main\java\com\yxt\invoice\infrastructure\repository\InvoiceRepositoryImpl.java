package com.yxt.invoice.infrastructure.repository;

import static com.yxt.order.types.invoice.remote.RemoteInvoiceStatus.openInvoiceSuccess;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.repository.RemoteDataResult;
import com.yxt.invoice.domain.repository.RemoteDataResult.RemoteDetail;
import com.yxt.invoice.domain.utils.OrderDateUtils;
import com.yxt.invoice.infrastructure.converter.InvoiceDOConverter;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.doc.EsInvoiceMain;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.mapper.EsInvoiceMainMapper;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceDetailDO;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceMainDO;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceDetailMapper;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceMainMapper;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryData;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryReq;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosResponse;
import com.yxt.invoice.infrastructure.provider.feign.HdPosFeign;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.invoice.enums.InvoiceIsValidEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.remote.RemoteInvoiceStatus;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 发票仓储实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Repository
@Slf4j
public class InvoiceRepositoryImpl implements InvoiceRepository {


  @Autowired
  private InvoiceMainMapper invoiceMainMapper;


  @Autowired
  private InvoiceDetailMapper invoiceDetailMapper;


  @Resource
  private HdPosFeign hdPosFeign;


  @Resource
  private EsInvoiceMainMapper esInvoiceMainMapper;


  @Override
  public PageDTO<InvoiceMain> pageInvoiceList(QueryInvoiceListCommand query) {
    LambdaEsQueryWrapper<EsInvoiceMain> queryWrapper = new LambdaEsQueryWrapper<>();

    queryWrapper.in(!CollectionUtils.isEmpty(query.getCompanyCodeList()),
        EsInvoiceMain::getCompanyCode, query.getCompanyCodeList());
    queryWrapper.in(!CollectionUtils.isEmpty(query.getOrganizationCodeList()),
        EsInvoiceMain::getOrganizationCode, query.getOrganizationCodeList());
    queryWrapper.eq(StringUtils.isNotEmpty(query.getThirdOrderNo()), EsInvoiceMain::getThirdOrderNo,
        query.getThirdOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(query.getPosNo()), EsInvoiceMain::getPosNo,
        query.getPosNo());
    queryWrapper.ge(Objects.nonNull(query.getApplyTimeStart()), EsInvoiceMain::getApplyTime,
        OrderDateUtils.formatYYMMDD(query.getApplyTimeStart()));
    queryWrapper.le(Objects.nonNull(query.getApplyTimeEnd()), EsInvoiceMain::getApplyTime,
        OrderDateUtils.formatYYMMDD(query.getApplyTimeEnd()));
    queryWrapper.eq(StringUtils.isNotEmpty(query.getInvoiceRedBlueType()),
        EsInvoiceMain::getInvoiceRedBlueType, query.getInvoiceRedBlueType());

    queryWrapper.eq(StringUtils.isNotEmpty(query.getInvoiceStatus()),
        EsInvoiceMain::getInvoiceStatus, query.getInvoiceStatus());
    queryWrapper.eq(StringUtils.isNotEmpty(query.getSyncStatus()), EsInvoiceMain::getSyncStatus,
        query.getSyncStatus());
    queryWrapper.eq(EsInvoiceMain::getIsValid, InvoiceIsValidEnum.VALID.getCode());

    queryWrapper.orderByDesc(EsInvoiceMain::getApplyTime);
    EsPageInfo<EsInvoiceMain> esPage = esInvoiceMainMapper.pageQuery(queryWrapper,
        query.getCurrentPage().intValue(), query.getPageSize().intValue());

    List<InvoiceMain> resList = Lists.newArrayList();
    if (esPage.getTotal() != 0L) {
      resList = esPage.getList().stream().map(esInvoiceMain -> {
        LambdaQueryWrapper<InvoiceMainDO> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(InvoiceMainDO::getInvoiceMainNo, esInvoiceMain.getInvoiceMainNo());
        InvoiceMainDO invoiceMainDO = invoiceMainMapper.selectOne(detailWrapper);
        return InvoiceDOConverter.toDomain(invoiceMainDO);
      }).collect(Collectors.toList());
    }
    PageDTO<InvoiceMain> pageRes = new PageDTO<>();
    pageRes.setTotalCount(esPage.getTotal());
    pageRes.setTotalPage((long) esPage.getPages());
    pageRes.setData(resList);
    pageRes.setCurrentPage(query.getCurrentPage());
    pageRes.setPageSize(query.getPageSize());
    return pageRes;
  }

  @Override
  public Boolean checkExistsFromDb(OrderExistsInvoiceQueryCommand command) {
    LambdaQueryWrapper<InvoiceMainDO> detailWrapper = new LambdaQueryWrapper<>();
    detailWrapper.eq(InvoiceMainDO::getOrderNo, command.getOrderNo())
        .eq(InvoiceMainDO::getIsValid, InvoiceIsValidEnum.VALID.getCode())
        .eq(InvoiceMainDO::getInvoiceRedBlueType, command.getInvoiceRedBlueType().getCode());
    return invoiceMainMapper.selectCount(detailWrapper) > 0;
  }

  @Override
  public RemoteDataResult checkExistsFromRemote(OrderExistsInvoiceQueryCommand command) {
    RemoteDataResult remoteDataResult = new RemoteDataResult();
    remoteDataResult.fail();

    PosInvoiceQueryReq req = new PosInvoiceQueryReq();
    req.setThirdOrderNo(command.getThirdOrderNo().getThirdOrderNo());
    req.setStoreCode(command.getStoreCode());

    PosResponse<List<PosInvoiceQueryData>> posResponse = hdPosFeign.invoiceQuery(req);
    Boolean success = posResponse.success();
    if (success) {
      List<PosInvoiceQueryData> dataList = posResponse.getData();
      if (CollectionUtils.isEmpty(dataList)) {
        return remoteDataResult;
      }

      // 主要看responseId有值的数据
      List<RemoteDetail> remoteDetailList = dataList.stream()
          .filter(s -> StringUtils.isNotEmpty(s.getResponseId())).filter(
              s -> openInvoiceSuccess(RemoteInvoiceStatus.getByCode(s.getInvoiceStatus()))) // 筛选成功的
          .map(s -> {
            RemoteDetail remoteDetail = new RemoteDetail();
            remoteDetail.setResponseId(s.getResponseId());
            remoteDetail.setInvoiceStatus(RemoteInvoiceStatus.getByCode(s.getInvoiceStatus()));
            return remoteDetail;
          }).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(remoteDetailList)) {
        remoteDataResult.success(remoteDetailList);
      }
      return remoteDataResult;
    } else {
      log.warn("调用海典POS查询单据是否存在,失败,请重试.响应:{}", JsonUtils.toJson(posResponse));
      return remoteDataResult;
    }

  }

  @Override
  @Transactional
  public void doSave(InvoiceAggregate aggregate) {
    InvoiceMain invoice = aggregate.getInvoiceMain();
    List<InvoiceDetail> invoiceDetailList = aggregate.getInvoiceDetailList();
    InvoiceMainDO invoiceMainDO = InvoiceDOConverter.toDO(invoice);
    List<InvoiceDetailDO> invoiceDetailDOList = invoiceDetailList.stream()
        .map(InvoiceDOConverter::toDO).collect(Collectors.toList());

    if (Objects.isNull(invoiceMainDO.getId())) {
      invoiceMainMapper.insert(invoiceMainDO);
    } else {
      invoiceMainMapper.updateById(invoiceMainDO);
    }

    if (!CollectionUtils.isEmpty(invoiceDetailDOList)) {
      for (InvoiceDetailDO invoiceDetailDO : invoiceDetailDOList) {
        if (Objects.isNull(invoiceDetailDO.getId())) {
          invoiceDetailMapper.insert(invoiceDetailDO);
        } else {
          invoiceDetailMapper.updateById(invoiceDetailDO);
        }
      }
    }
  }

  @Override
  @Transactional
  public void doSaveRedInvoice(InvoiceAggregate redAggregate) {
    // 持久化红冲
    doSave(redAggregate);
  }

  @Override
  public List<InvoiceAggregate> findRedInvoiceByOrderNo(String orderNo) {
    return innerInvoiceQuery(orderNo, InvoiceRedBlueTypeEnum.CREDIT_NOTE);
  }

  @Override
  public List<InvoiceAggregate> findInvoiceByOrderNo(String orderNo) {
    return innerInvoiceQuery(orderNo, InvoiceRedBlueTypeEnum.TAX_INVOICE);
  }

  @Nullable
  private List<InvoiceAggregate> innerInvoiceQuery(String orderNo,
      InvoiceRedBlueTypeEnum invoiceRedBlueTypeEnum) {
    // 使用LambdaQueryWrapper查询主表
    LambdaQueryWrapper<InvoiceMainDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceMainDO::getOrderNo, orderNo)
        .eq(InvoiceMainDO::getInvoiceRedBlueType, invoiceRedBlueTypeEnum.getCode())
        .eq(InvoiceMainDO::getIsValid, InvoiceIsValidEnum.VALID.getCode());

    List<InvoiceMainDO> invoiceMainDOList = invoiceMainMapper.selectList(wrapper);
    if (invoiceMainDOList.isEmpty()) {
      return null;
    }
    List<InvoiceAggregate> invoiceAggregateList = new ArrayList<>();
    for (InvoiceMainDO invoiceMainDO : invoiceMainDOList) {

      // 使用LambdaQueryWrapper查询明细
      LambdaQueryWrapper<InvoiceDetailDO> detailWrapper = new LambdaQueryWrapper<>();
      detailWrapper.eq(InvoiceDetailDO::getInvoiceMainNo, invoiceMainDO.getInvoiceMainNo())
          .eq(InvoiceDetailDO::getIsValid, InvoiceIsValidEnum.VALID.getCode());

      List<InvoiceDetailDO> detailDOList = invoiceDetailMapper.selectList(detailWrapper);

      // 转换为Domain对象并重建聚合根
      InvoiceMain invoice = InvoiceDOConverter.toDomain(invoiceMainDO);
      List<InvoiceDetail> details = InvoiceDOConverter.toDetailDomainList(detailDOList);
      invoiceAggregateList.add(InvoiceAggregate.build(invoice, details));
    }

    return invoiceAggregateList;
  }

  @Override
  public InvoiceAggregate findByInvoiceMainNo(String invoiceMainNo) {
    log.info("根据开票单号查询发票，开票单号：{}", invoiceMainNo);

    // 使用LambdaQueryWrapper查询主表
    LambdaQueryWrapper<InvoiceMainDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceMainDO::getInvoiceMainNo, invoiceMainNo);
    wrapper.eq(InvoiceMainDO::getIsValid, InvoiceIsValidEnum.VALID.getCode());

    return getInvoiceAggregate(invoiceMainNo, wrapper);
  }

  @Override
  public InvoiceAggregate findByRedInvoiceMainNo(String redInvoiceMainNo) {
    LambdaQueryWrapper<InvoiceMainDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceMainDO::getRedInvoiceMainNo, redInvoiceMainNo);
    wrapper.eq(InvoiceMainDO::getIsValid, InvoiceIsValidEnum.VALID.getCode());
    return getInvoiceAggregate(redInvoiceMainNo, wrapper);
  }



  private InvoiceAggregate getInvoiceAggregate(String invoiceMainNo,
      LambdaQueryWrapper<InvoiceMainDO> wrapper) {
    InvoiceMainDO invoiceMainDO = invoiceMainMapper.selectOne(wrapper);
    if (invoiceMainDO == null) {
      return null;
    }

    // 使用LambdaQueryWrapper查询明细
    LambdaQueryWrapper<InvoiceDetailDO> detailWrapper = new LambdaQueryWrapper<>();
    detailWrapper.eq(InvoiceDetailDO::getInvoiceMainNo, invoiceMainNo);

    List<InvoiceDetailDO> detailDOList = invoiceDetailMapper.selectList(detailWrapper);

    // 转换为Domain对象并重建聚合根
    InvoiceMain invoice = InvoiceDOConverter.toDomain(invoiceMainDO);
    List<InvoiceDetail> details = InvoiceDOConverter.toDetailDomainList(detailDOList);

    return InvoiceAggregate.build(invoice, details);
  }

}
