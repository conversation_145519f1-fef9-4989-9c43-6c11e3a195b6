package com.yxt.invoice.sdk.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class InvoiceDetailDTO {

    /**
     * 明细ID
     */
    @ApiModelProperty(value = "明细ID", example = "2001")
    private Long id;

    /**
     * 开票单号
     */
    @ApiModelProperty(value = "开票单号", example = "INV20250811001")
    private String invoiceMainNo;

    /**
     * 开票单明细号
     */
    @ApiModelProperty(value = "开票单明细号", example = "INV20250811001-001")
    private String invoiceDetailNo;

    /**
     * 行号
     */
    @ApiModelProperty(value = "行号", example = "1")
    private String rowNo;

    @ApiModelProperty(value = "发票行号", example = "1")
    private String line;

    /**
     * 税收分类编码
     */
    @ApiModelProperty(value = "税收分类编码", example = "1070324990000000000")
    private String taxClassificationCode;

    /**
     * 税收分类编码父级分类名称
     */
    @ApiModelProperty(value = "税收分类编码父级分类名称", example = "药品")
    private String topLevelTaxClassificationCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码", example = "DRUG001")
    private String erpCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称", example = "阿莫西林胶囊")
    private String erpName;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量", example = "2.00")
    private BigDecimal commodityCount;

    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格", example = "0.25g*24粒/盒")
    private String commoditySpec;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位", example = "盒")
    private String unit;

    /**
     * 商品售价
     */
    @ApiModelProperty(value = "商品售价", example = "25.00")
    private BigDecimal price;

    /**
     * 商品总额=price*数量
     */
    @ApiModelProperty(value = "商品总额", example = "50.00")
    private BigDecimal totalAmount;

    /**
     * 行税额行金额/(1+税率)*税率
     */
    @ApiModelProperty(value = "行税额", example = "6.50")
    private BigDecimal taxAmount;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率", example = "0.13")
    private BigDecimal taxRate;

    /**
     * 税率编码
     */
    @ApiModelProperty(value = "税率编码", example = "13%")
    private String taxRateCode;

    /**
     * 价税合计
     */
    @ApiModelProperty(value = "价税合计", example = "56.50")
    private BigDecimal priceTaxAmount;

    /**
     * 行性质 REGULAR_LINE-正常行 DISCOUNT_LINE-折扣行 DISCOUNTED_LINE-被折扣行
     */
    @ApiModelProperty(value = "行性质", example = "REGULAR_LINE", allowableValues = "REGULAR_LINE,DISCOUNT_LINE,DISCOUNTED_LINE")
    private String invoiceLineType;

    /**
     * 抵扣金额
     */
    @ApiModelProperty(value = "抵扣金额", example = "0.00")
    private BigDecimal discountAmount;

    /**
     * 启动优惠政策 YES-启用 NO-不启用
     */
    @ApiModelProperty(value = "启动优惠政策", example = "NO", allowableValues = "YES,NO")
    private String policyStatus;

    /**
     * 优惠标识
     */
    @ApiModelProperty(value = "优惠标识", example = "")
    private String policyTag;

    /**
     * 优惠税率
     */
    @ApiModelProperty(value = "优惠税率", example = "0.00")
    private BigDecimal policyTaxRate;

    /**
     * 是否起效 1-起效 -1-未起效
     */
    @ApiModelProperty(value = "是否起效 1-起效 -1-未起效")
    private Long isValid;

    /**
     * 平台创建时间
     */
    @ApiModelProperty(value = "平台创建时间", example = "2025-08-11 14:30:00")
    private Date created;

    /**
     * 平台更新时间
     */
    @ApiModelProperty(value = "平台更新时间", example = "2025-08-11 14:35:00")
    private Date updated;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "system")
    private String createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "system")
    private String updatedBy;

    /**
     * 系统创建时间
     */
    @ApiModelProperty(value = "系统创建时间", example = "2025-08-11 14:30:00")
    private Date sysCreateTime;

    /**
     * 系统更新时间
     */
    @ApiModelProperty(value = "系统更新时间", example = "2025-08-11 14:35:00")
    private Date sysUpdateTime;

    /**
     * 数据版本，每次update+1
     */
    @ApiModelProperty(value = "数据版本", example = "1")
    private Long version;


}
