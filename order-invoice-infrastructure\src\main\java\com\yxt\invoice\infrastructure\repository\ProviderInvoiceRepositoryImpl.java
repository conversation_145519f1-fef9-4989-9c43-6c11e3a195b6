package com.yxt.invoice.infrastructure.repository;

import static com.yxt.order.types.invoice.remote.RemoteInvoiceStatus.BLUE_INVOICE_APPLY_FAILED;

import com.google.common.collect.Lists;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.ProviderInvoiceRepository;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties.TaxCloud;
import com.yxt.invoice.infrastructure.common.utils.LogUtils;
import com.yxt.invoice.infrastructure.provider.convert.TaxCloudConvert;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceRequest;
import com.yxt.invoice.infrastructure.provider.dto.req.invoice.InvoiceRequestData;
import com.yxt.invoice.infrastructure.provider.dto.req.red_invoice.RedInvoiceRequest;
import com.yxt.invoice.infrastructure.provider.dto.req.red_invoice.RedInvoiceRequestData;
import com.yxt.invoice.infrastructure.provider.dto.res.invoice.InvoiceResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.invoice.data.ApplyInvoiceResponseData;
import com.yxt.invoice.infrastructure.provider.dto.res.red_invoice.RedInvoiceResponse;
import com.yxt.invoice.infrastructure.provider.dto.res.red_invoice.data.RedInvoiceResponseData;
import com.yxt.invoice.infrastructure.provider.feign.TaxCloudFeign;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.invoice.enums.InvoiceLogTypeApiEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.remote.RemoteInvoiceStatus;
import com.yxt.order.types.invoice.remote.RemoteWriteOffState;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 发票仓储接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
@Component
public class ProviderInvoiceRepositoryImpl implements ProviderInvoiceRepository {

  @NotNull
  private String getPosition() {
    return this.getClass().getName() + ":tId:" + Thread.currentThread().getId();
  }

  @Resource
  private RemoteProperties remoteProperties;

  @Resource
  private TaxCloudFeign taxCloudFeign;


  @Override
  public void applyInvoice(InvoiceAggregate aggregate) {
    TaxCloud taxCloud = remoteProperties.getTaxCloud();
    InvoiceRequest apiReq = new InvoiceRequest();
    apiReq.setPId(taxCloud.getPId());
    apiReq.setPSecret(taxCloud.getPSecret());
    InvoiceRequestData invoiceRequestData = TaxCloudConvert.buildInvoiceRequest(aggregate);
    apiReq.setData(Lists.newArrayList(invoiceRequestData));

    apiReq.valid(); // 校验

    InvoiceResponse response = taxCloudFeign.applyInvoice(apiReq);
    //返回处理
    if (response.success() && !CollectionUtils.isEmpty(response.getData())) {
      ApplyInvoiceResponseData data = response.getData().get(0); // 只会一单一单请求,所以也只会返回1个

      InvoiceStatusEnum invoiceStatus = RemoteInvoiceStatus.mappingInvoiceStatus(
          data.getInvoiceStatus());

      String errorMessage = Strings.EMPTY;
      // 开票失败获取失败原因
      if (BLUE_INVOICE_APPLY_FAILED.getCode().equals(data.getInvoiceStatus())) {
        errorMessage = data.getStatusMsg();
      }

      String invoiceCode = data.getInvoiceCode();
      String responseId = data.getResponseId();
      // 调用API失败
      aggregate.invokeApiError(invoiceCode, invoiceStatus, errorMessage, responseId);
    }else if(response.error()){
      throw new RuntimeException(String.format("税务云系统报错: %s",response.getMsg()));
    }else {
      throw new RuntimeException(String.format("税务云返回的code,系统无法处理,需要研发介入。税务云原始响应信息: %s",JsonUtils.toJson(response)));
    }
  }


  @Override
  @Transactional
  public void applyRedInvoice(InvoiceAggregate redAggregate) {
    RedInvoiceRequest apiReq = new RedInvoiceRequest();
    apiReq.setPId(remoteProperties.getTaxCloud().getPId());
    apiReq.setPSecret(remoteProperties.getTaxCloud().getPSecret());
    RedInvoiceRequestData negativeInvoiceData = TaxCloudConvert.buildRedRequest(redAggregate);
    apiReq.setData(negativeInvoiceData);

    apiReq.valid();

    RedInvoiceResponse response = taxCloudFeign.applyRedInvoice(apiReq);
    //返回处理
    if (response.success() && response.getData() != null) {
      RedInvoiceResponseData resDto = response.getData();
      String responseId = resDto.getResponseId();

      // 更新红票信息
      RemoteWriteOffState remoteWriteOffState = RemoteWriteOffState.getByCode(resDto.getWriteOffState());
      InvoiceMain redInvoiceMain = redAggregate.getInvoiceMain();
      redInvoiceMain.setRemoteInvoiceResponseId(responseId);
      redInvoiceMain.setRemoteWriteOffState(remoteWriteOffState); // 需要依据此字段,写定时任务
      // 不应该更新蓝票状态。蓝票始终是开票成功的。对应一个红冲的票据，只需要管理红冲的票据即可
    }else if(response.error()){
      throw new RuntimeException(String.format("红冲 税务云系统报错: %s",response.getMsg()));
    }else {
      throw new RuntimeException(String.format("红冲 税务云返回的code,系统无法处理,需要研发介入。税务云原始响应信息: %s",JsonUtils.toJson(response)));
    }

  }
}
