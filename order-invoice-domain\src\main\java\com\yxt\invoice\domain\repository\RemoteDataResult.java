package com.yxt.invoice.domain.repository;

import com.yxt.order.types.invoice.remote.RemoteInvoiceStatus;
import java.util.List;
import lombok.Data;

@Data
public class RemoteDataResult {

  private Boolean result;

  private List<RemoteDetail> remoteDetailList;

  @Data
  public static class RemoteDetail {
    private String responseId;
    private RemoteInvoiceStatus invoiceStatus;
  }

  public void fail(){
    this.result = Boolean.FALSE;
  }

  public void success(List<RemoteDetail> remoteDetailList){
    this.result = Boolean.TRUE;
    this.remoteDetailList = remoteDetailList;
  }

}
