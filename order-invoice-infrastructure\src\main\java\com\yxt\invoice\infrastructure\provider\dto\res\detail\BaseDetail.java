package com.yxt.invoice.infrastructure.provider.dto.res.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import lombok.Data;

@Data
public class BaseDetail {

  @JsonProperty("outRequestCode")
  private String outRequestCode;

  @JsonProperty("invoiceTag")
  private String invoiceTag;

  @JsonProperty("pId")
  private String pId;

  @JsonProperty("channel")
  private String channel;

  @JsonProperty("responseId")
  private String responseId;

  @JsonProperty("invoiceStatus")
  private String invoiceStatus;

  @JsonProperty("uploadStatus")
  private String uploadStatus;

  @JsonProperty("statusMsg")
  private String statusMsg;

  @JsonProperty("resetInvoice")
  private String resetInvoice;

  @JsonProperty("invoicePdf")
  private String invoicePdf;

  @JsonProperty("downPdf")
  private String downPdf;

  @JsonProperty("issueDate")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date issueDate;

  @JsonProperty("invoiceCode")
  private String invoiceCode;

  @JsonProperty("requestCode")
  private String requestCode;

  /**
   * 纳税人识别号
   */
  @JsonProperty("sellerTin")
  private String sellerTin;


  @JsonProperty("invoiceId")
  private Long invoiceId;
}
