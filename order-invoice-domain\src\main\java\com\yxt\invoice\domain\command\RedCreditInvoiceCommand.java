package com.yxt.invoice.domain.command;

import com.yxt.order.types.invoice.remote.RemoteRedInvoiceReason;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票红冲命令
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@NoArgsConstructor
@Data
public class RedCreditInvoiceCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 蓝票单号不能为空
     */
    @NotBlank(message = "蓝票单号不能为空")
    private String invoiceMainNo;

    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    private RemoteRedInvoiceReason redCreditReason;
    /**
     * 备注
     */
    private String notes;
    /**
     * 操作人用户ID
     */
    private String operatorUserId;




}
