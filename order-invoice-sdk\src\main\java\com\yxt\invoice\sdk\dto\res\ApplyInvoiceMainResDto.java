package com.yxt.invoice.sdk.dto.res;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 */
@Data
public class ApplyInvoiceMainResDto {

     @ApiModelProperty(value = "返回编码 1000=success other fail")
     private String  code;

    /**
     * 商户编码
     */
    @ApiModelProperty(value = "商户编码", example = "MER001")
    private String merCode;
    /**
     * 平台编码
     */
    @ApiModelProperty(value = "平台编码", example = "YXDJ")
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @ApiModelProperty(value = "第三方平台订单号", example = "THIRD20250811001")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", example = "ORDER20250811001")
    private String orderNo;

    /**
     * pos销售单号
     */
    @ApiModelProperty(value = "POS销售单号", example = "POS20250811001")
    private String posNo;

    /**
     * 开票单号
     */
    @ApiModelProperty(value = "开票单号")
    private String invoiceMainNo;

    /**
     * 发票号码（税务云回调写入）
     */
    @ApiModelProperty(value = "发票号码")
    private String remoteInvoiceCode;


    /**
     * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
     */
    @ApiModelProperty("交易场景 ONLINE:线上交易, OFFLINE:线下交易")
    private String transactionChannel;
    /**
     * 会员编号
     */
    @ApiModelProperty("会员编号")
    private String userId;

    /**
     * 蓝票:TAX_INVOICE 红票:CREDIT_NOTE
     */
    @ApiModelProperty("蓝票:TAX_INVOICE 红票:CREDIT_NOTE")
    private String invoiceRedBlueType;



    /**
     * 发票状态
     */
    @ApiModelProperty("待开票:WAIT 开票中:PROCESS 失败:FAIL 成功:SUCCESS 红冲中:RED_PROCESS 红冲成功:RED_SUCCESS  ")
    private String invoiceStatus;

    /**
     * 电子发票PDF地址
     */
    @ApiModelProperty("电子发票PDF地址")
    private String pdfUrl;

    /**
     * 失败原因
     */
    @ApiModelProperty("失败原因")
    private String invoiceErrMsg;

    /**
     * 数据版本，每次update+1
     */
    @ApiModelProperty("数据版本，每次update+1")
    private Long version;

    public static ApplyInvoiceMainResDto fail() {
        ApplyInvoiceMainResDto res = new ApplyInvoiceMainResDto();
        res.setCode("1");
        res.setInvoiceErrMsg("失败");
        return res;
    }
}
