package com.yxt.invoice.domain.model;

import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import com.yxt.order.types.invoice.remote.RemoteRedInvoiceReason;
import com.yxt.order.types.invoice.remote.RemoteWriteOffState;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

/**
 * 发票聚合根 根据数据库表 invoice_main 设计
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class InvoiceMain {

  /**
   * 发票ID
   */
  private Long id;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 所属机构名称
   */
  private String organizationName;

  /**
   * 开票单号
   */
  private String invoiceMainNo;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 第三方平台订单号
   */
  private OfflineThirdOrderNo thirdOrderNo;

  /**
   * 订单号
   */
  private OfflineOrderNo orderNo;

  /**
   * pos销售单号
   */
  private String posNo;


  /**
   * 会员编号
   */
  private String userId;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
   */
  private String transactionChannel;


  /**
   * 业务类型  O2O、B2C、B2B、OFFLINE
   */
  private String businessType;


  /**
   * 蓝票:TAX_INVOICE 红票:CREDIT_NOTE
   */
  private InvoiceRedBlueTypeEnum invoiceRedBlueType;

  /**
   * 红冲对应原发票号,红票必填
   */
  private String redInvoiceMainNo;

  /**
   * INVOICE_ERROR-开票有误 GOODS_RETURN-销货退回 SERVICE_TERMINATION-服务终止 SALES_ALLOWANCE-销售折让
   */
  private RemoteRedInvoiceReason redInvoiceReason;

  /**
   * 备注
   */
  private String notes;

  /**
   * 发票类型
   */
  private InvoiceTypeEnum invoiceType;

  /**
   * 发票状态
   */
  private InvoiceStatusEnum invoiceStatus;

  /**
   * 同步状态
   */
  private InvoiceSyncStatusEnum syncStatus;

  /**
   * 实付金额
   */
  private BigDecimal actualPayAmount;

  /**
   * 配送费
   */
  private BigDecimal deliveryAmount;

  /**
   * 配送方式 PLATFORM_FULFILLMENT:平台配送 MERCHANT_FULFILLMENT:商家自配  SelfPickup:自取
   */
  private String deliveryType;

  /**
   * 发票金额
   */
  private BigDecimal invoiceAmount;

  /**
   * 税额
   */
  private BigDecimal taxAmount;

  /**
   * 价税合计
   */
  private BigDecimal priceTaxAmount;

  /**
   * 拆票标记 SPLIT-拆 NOT-不拆
   */
  private String splitBill;

  /**
   * 订单创单时间
   */
  private Date orderCreated;

  /**
   * 电子发票PDF地址
   */
  private String pdfUrl;

  /**
   * 失败原因
   */
  private String invoiceErrMsg;

  /**
   * 申请开票时间
   */
  private Date applyTime;

  /**
   * 开票完成时间
   */
  private Date completeTime;

  /**
   * 开票人
   */
  private String operator;

  /**
   * 收款人
   */
  private String payee;

  /**
   * 复核人
   */
  private String reviewed;

  /**
   * 申请渠道 一心到家-YXDJ 心云-XY 海典H2-H2POS
   */
  private String applyChannel;

  /**
   * 开票主体
   */
  private String sellerNumber;

  /**
   * 开票主体名称
   */
  private String sellerName;

  /**
   * 开票纳税人识别号
   */
  private String sellerTin;

  /**
   * 开票主体地址
   */
  private String sellerAddress;

  /**
   * 开票主体电话
   */
  private String sellerPhone;

  /**
   * 开票主体银行
   */
  private String sellerBank;

  /**
   * 开票主体银行账户
   */
  private String sellerBankAccount;

  /**
   * 购方类型
   */
  private InvoiceBuyerPartyTypeEnum buyerPartyType;

  /**
   * 购方名称
   */
  private String buyerName;

  /**
   * 购方税号（个人身份证/单位纳税人识别号）
   */
  private String buyerTin;

  /**
   * 购方地址
   */
  private String buyerAddress;

  /**
   * 购方电话
   */
  private String buyerPhone;

  /**
   * 购方银行
   */
  private String buyerBank;

  /**
   * 购方银行账户
   */
  private String buyerBankAccount;

  /**
   * 购方邮箱
   */
  private String buyerEmail;

  /**
   * 购方手机号
   */
  private String buyerMobile;

  /**
   * 显示购方银行账户 SHOW-显示 HIDE-不显示
   */
  private String showBuyerBankAccount;

  /**
   * 是否起效 1-起效 -1-未起效
   */
  private Long isValid;
  /**
   * 供应商参数
   * List<ProviderParam></>
   */
//    private String providerParam;


  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;


  /**
   * 发票号码
   */
  private String remoteInvoiceCode;


  private String remoteInvoiceResponseId;

  /**
   * 红票写入阶段 PENDING-待处理 PROCESSING-处理中 COMPLETED-处理完成 ERROR-错误
   */
  private RemoteWriteOffState remoteWriteOffState;

  /**
   * 数据来源: INVOICE-心云开票,REMOTE-远程拉取
   */
  private String dataSource;


  public void redApply(String redInvoiceMainNo, RedCreditInvoiceCommand command) {
    String operatorUserId = command.getOperatorUserId();
    RemoteRedInvoiceReason redCreditReason = command.getRedCreditReason();
    String notes = command.getNotes();

    this.id = null;
    this.invoiceRedBlueType = InvoiceRedBlueTypeEnum.CREDIT_NOTE;
    this.redInvoiceReason = redCreditReason;
    this.notes = notes;
    this.invoiceStatus = InvoiceStatusEnum.WAIT;
    this.syncStatus = InvoiceSyncStatusEnum.WAIT;
    this.pdfUrl = null;
    this.invoiceErrMsg = null;
    this.applyTime = new Date();
    this.operator = operatorUserId;
    this.created = new Date();
    this.updated = new Date();
    this.invoiceMainNo = redInvoiceMainNo; // 红冲单号
    this.remoteInvoiceCode = null;
    this.remoteInvoiceResponseId = null;
  }

  public boolean isRedCreditInvoice() {
    return InvoiceRedBlueTypeEnum.CREDIT_NOTE.equals(this.invoiceRedBlueType);
  }

  /**
   * 清空开票主体和购方主体信息
   */
  public void cleanSellerAndBuyerInfo() {
    this.sellerNumber = Strings.EMPTY;
    this.sellerName = Strings.EMPTY;
    this.sellerTin = Strings.EMPTY;
    this.sellerAddress = Strings.EMPTY;
    this.sellerPhone = Strings.EMPTY;
    this.sellerBank = Strings.EMPTY;
    this.sellerBankAccount = Strings.EMPTY;
    this.buyerPartyType = null;
    this.buyerName = Strings.EMPTY;
    this.buyerTin = Strings.EMPTY;
    this.buyerAddress = Strings.EMPTY;
    this.buyerPhone = Strings.EMPTY;
    this.buyerBank = Strings.EMPTY;
    this.buyerBankAccount = Strings.EMPTY;
    this.buyerEmail = Strings.EMPTY;
    this.buyerMobile = Strings.EMPTY;
  }

  public void resetRedInvoice() {
    this.id = null;
    this.invoiceMainNo = Strings.EMPTY;
    this.invoiceStatus = InvoiceStatusEnum.WAIT_RED;
    this.invoiceRedBlueType = InvoiceRedBlueTypeEnum.CREDIT_NOTE;
    this.pdfUrl = Strings.EMPTY;
    this.invoiceErrMsg = Strings.EMPTY;
    this.applyTime = null;
    this.completeTime = null;
    this.operator = null; // 开票人

//    seller不用重置,直接复制过来。红冲的时候需要使用，例如sellerNumber（销方,店铺号）
//    this.reviewed = null;
//    this.sellerNumber = Strings.EMPTY;
//    this.sellerName = Strings.EMPTY;
//    this.sellerTin = Strings.EMPTY;
//    this.sellerAddress = Strings.EMPTY;
//    this.sellerPhone = Strings.EMPTY;
//    this.sellerBank = Strings.EMPTY;
//    this.sellerBankAccount = Strings.EMPTY;
//    this.buyerPartyType = null;
//    this.buyerName = Strings.EMPTY;
//    this.buyerTin = Strings.EMPTY;
//    this.buyerAddress = Strings.EMPTY;
//    this.buyerPhone = Strings.EMPTY;
//    this.buyerBank = Strings.EMPTY;
//    this.buyerBankAccount = Strings.EMPTY;
//    this.buyerEmail = Strings.EMPTY;
//    this.buyerMobile = Strings.EMPTY;
//    this.showBuyerBankAccount = Strings.EMPTY;

    this.createdBy = null;
    this.updatedBy = null;
    this.sysCreateTime = null;
    this.sysUpdateTime = null;
    this.remoteInvoiceCode = Strings.EMPTY;
    this.remoteInvoiceResponseId = Strings.EMPTY;
    this.syncStatus = InvoiceSyncStatusEnum.WAIT;
  }
}
