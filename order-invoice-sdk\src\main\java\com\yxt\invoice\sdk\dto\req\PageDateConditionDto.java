package com.yxt.invoice.sdk.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月23日 11:25
 * @email: <EMAIL>
 */
@Data
public class PageDateConditionDto  {

  /**
   * 查询条件 格式2024-04-22 00:00:00
   */
//  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//  @ApiModelProperty(value = "开始时间", example = "2025-08-01 00:00:00")
//  private Date startDate;
//
//  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//  @ApiModelProperty(value = "结束时间", example = "2025-08-11 23:59:59")
//  private Date endDate;

  /**
   * 当前页码
   */
  @ApiModelProperty(value = "当前页码", example = "1")
  private Long currentPage = 1L;
  /**
   * 每页数量
   */
  @ApiModelProperty(value = "每页数量", example = "20")
  private Long pageSize = 20L;

}
