package com.yxt.invoice.application.third.order.feign;


import com.yxt.domain.order.BusinessOrderWebServiceName;
import com.yxt.domain.order.refund_query.B2CRefundQueryDomainApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * B2C接口
 *
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@FeignClient(value = BusinessOrderWebServiceName.value)
public interface B2CRefundQueryDomainApiFeign extends B2CRefundQueryDomainApi {


}
