package com.yxt.invoice.infrastructure.provider.dto.res.detail.red_invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.BaseDetail;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票明细
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RedInvoiceDetailResponseData extends BaseDetail {



  /********仅红票返回 start********/

  @JsonProperty("writeOffState")
  private String writeOffState;

  /**
   * 红字冲销金额
   */
  @JsonProperty("hzcxje")
  private BigDecimal hzcxje;

  /**
   * 红字冲销金额
   */
  @JsonProperty("hzcxse")
  private BigDecimal hzcxse;


  /**
   * 红字确认单状态代码 00:未确认 01:确认中 02:确认失败 03:确认完成
   */
  @JsonProperty("hzqrxxztDm")
  private String hzqrxxztDm;


  @JsonProperty("uuid")
  private String uuid;

  /**
   * 红字确认单编号
   */
  @JsonProperty("hzfpxxqrdbh")
  private String hzfpxxqrdbh;


  /**
   * 冲红原因: 01：开票有误 02：销货退回 03：服务终止 04：销售折让
   */
  @JsonProperty("chyyDm")
  private String chyyDm;


  @JsonProperty("positiveInvoiceNo")
  private String positiveInvoiceNo;

  /********仅红票返回 end********/


}