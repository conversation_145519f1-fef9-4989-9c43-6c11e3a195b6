package com.yxt.invoice.sdk.dto.req;

import com.yxt.invoice.sdk.dto.InvoiceDetailDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 发票申请请求
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class ApplyInvoiceReqDto  implements Serializable {

    private static final long serialVersionUID = 1L;
    @NotEmpty(message = "发票信息不能为空")
    @Valid
    @ApiModelProperty(value = "发票主信息", required = true)
    private InvoiceMainDTO invoiceMain;

    /**
     * 发票明细列表
     */
    @NotEmpty(message = "发票明细不能为空")
    @Valid
    @ApiModelProperty(value = "发票明细列表", required = true)
    private List<InvoiceDetailDTO> details;




}
