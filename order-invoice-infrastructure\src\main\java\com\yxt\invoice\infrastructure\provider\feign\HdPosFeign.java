package com.yxt.invoice.infrastructure.provider.feign;


import com.yxt.invoice.infrastructure.common.api_log.ApiLog;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryData;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryReq;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosResponse;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 海典Pos接口
 *
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@FeignClient(name = "hd-pos-service", url = "${remote.pos.url}")
public interface HdPosFeign {


  @PostMapping("/api/pos/invoice/query")
  @ApiLog(value = "调用POS接口查询有无历史开票信息",businessNo = "#req.thirdOrderNo")
  PosResponse<List<PosInvoiceQueryData>> invoiceQuery(@RequestBody PosInvoiceQueryReq req);

}
