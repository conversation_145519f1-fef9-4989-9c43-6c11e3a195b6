package com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler.data.CanalInvoice;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler.data.CanalInvoice.Invoice;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.model.EsInvoiceMainModel;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class InvoiceMainHandler extends AbstractCanalHandler<CanalInvoice, EsInvoiceMainModel> {


  public InvoiceMainHandler() {
    super(CanalInvoice.class);
  }


  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.ORDER_INVOICE.equals(database) && table.equals(Table.INVOICE_MAIN);
  }

  @Override
  protected List<EsInvoiceMainModel> assemble() {

    List<Invoice> invoiceList = getData().getData();
    if (CollectionUtils.isEmpty(invoiceList)) {
      return Lists.newArrayList();
    }

    return invoiceList.stream().map(invoice -> {
      EsInvoiceMainModel esOrderModel = new EsInvoiceMainModel();
      esOrderModel.setInvoiceMainNo(invoice.getInvoiceMainNo());
      esOrderModel.setUserId(invoice.getUserId());
      esOrderModel.setCompanyCode(invoice.getCompanyCode());
      esOrderModel.setOrganizationCode(invoice.getOrganizationCode());
      esOrderModel.setThirdPlatformCode(invoice.getThirdPlatformCode());
      esOrderModel.setThirdOrderNo(invoice.getThirdOrderNo());
      esOrderModel.setOrderNo(invoice.getOrderNo());
      esOrderModel.setPosNo(invoice.getPosNo());
      esOrderModel.setInvoiceRedBlueType(invoice.getInvoiceRedBlueType());
      esOrderModel.setInvoiceStatus(invoice.getInvoiceStatus());
      esOrderModel.setBuyerPartyType(invoice.getBuyerPartyType());
      esOrderModel.setSyncStatus(invoice.getSyncStatus());
      esOrderModel.setIsValid(invoice.getIsValid());
      esOrderModel.setApplyTime(invoice.getApplyTime());
      return esOrderModel;
    }).collect(Collectors.toList());
  }
}
