package com.yxt.invoice.infrastructure.provider.dto.res.detail.invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.invoice.infrastructure.provider.dto.res.detail.BaseDetail;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发票明细
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceDetailResponseData  extends BaseDetail {
  /********仅蓝票返回 start********/
  /**
   * 蓝票税额
   */
  @JsonProperty("taxAmount")
  private BigDecimal taxAmount;


  @JsonProperty("invoiceAmount")
  private BigDecimal invoiceAmount;

  @JsonProperty("priceTaxAmount")
  private BigDecimal priceTaxAmount;
  /********仅蓝票返回 end********/


}