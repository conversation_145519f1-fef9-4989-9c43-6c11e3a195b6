package com.yxt.invoice.interfaces.service;

import com.yxt.invoice.domain.command.*;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.lang.dto.api.PageDTO;

/**
 * 发票服务接口
 * Service层，使用Command传参
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceService {


    /**
     * 申请开票
     *
     * @param command 开票申请命令
     * @return 发票聚合根
     */
    String applyInvoice(ApplyInvoiceCommand command);


    /**
     * 发票红冲
     *
     * @param command 红冲命令
     * @return 发票聚合根
     */
    String applyRedInvoice(RedCreditInvoiceCommand command);


    /**
     * 查询订单是否存在发票
     * @param command
     * @return
     */
    ExistsOrderInvoice queryOrderExistsInvoice(OrderExistsInvoiceQueryCommand command);

    /**
     * 查询三方订单是否存在发票
     * @param command
     * @return
     */
    ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(ExistsThirdOrderInvoiceCommand command);


    /**
     * 查询发票列表
     *
     * @param query 列表查询
     * @return 发票聚合根列表
     */
    PageDTO<InvoiceMain> pageInvoiceList(QueryInvoiceListCommand query);

    /**
     * 查询发票详情
     *
     * @param query 详情查询
     * @return 发票聚合根
     */
    InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query);




}
