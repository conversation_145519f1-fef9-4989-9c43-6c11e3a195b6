package com.yxt.invoice.sdk.dto.req;

import com.yxt.order.types.invoice.remote.RemoteRedInvoiceReason;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ApplyRedInvoiceMainReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户编码
     */
    @ApiModelProperty("商户编码,必填")
    private String merCode;


    /**
     * 红冲对应原发票号,红票必填
     */
    @ApiModelProperty("红冲对应原发票号,红票必填")
    private String targetInvoiceMainNo;

    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    @ApiModelProperty("红冲原因,INVOICE_ERROR-开票有误 GOODS_RETURN-销货退回 SERVICE_TERMINATION-服务终止 SALES_ALLOWANCE-销售折让")
    private RemoteRedInvoiceReason redInvoiceReason;

    /**
     * 备注
     */
    @ApiModelProperty("备注,可选")
    private String notes;

    /**
     * 操作人
     */
    @ApiModelProperty("操作人,必填")
    private String operatorUserId;


}
