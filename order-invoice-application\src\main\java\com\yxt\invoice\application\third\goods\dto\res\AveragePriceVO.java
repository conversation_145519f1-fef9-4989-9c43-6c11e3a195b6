package com.yxt.invoice.application.third.goods.dto.res;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年01月30日 9:49
 * @email: <EMAIL>
 */
@Data
public class AveragePriceVO {

    /**
     * 商品编码
     */
    private String erpCode;

    /**
     *  平均加权采购价(不含税)
     */
    private BigDecimal averagePrice;


    /**
     *  参考零售价
     */
    private BigDecimal refRetailPrice;

    private String specId;

    /**
     * 含税成本价
     */
    private BigDecimal taxPrice;

    /**
     * 税率
     */
    private String taxRate;


}