package com.yxt.invoice.domain.model.aggregate;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.yxt.common.ddd.domain.model.BaseAggregateRoot;
import com.yxt.invoice.domain.command.OrderExistsInvoiceQueryCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.event.BaseInvoiceDomainEvent;
import com.yxt.invoice.domain.event.callback.CallbackInvoiceDetail;
import com.yxt.invoice.domain.event.callback.CallbackRedInvoiceDetail;
import com.yxt.invoice.domain.event.create.PullBackInvoiceInfoEvent;
import com.yxt.invoice.domain.external.IdService;
import com.yxt.invoice.domain.external.IdService.IdType;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.repository.RemoteDataResult.RemoteDetail;
import com.yxt.order.types.invoice.enums.InvoiceDataSourceEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.logging.log4j.util.Strings;

@EqualsAndHashCode(callSuper = true)
@Data
public class InvoiceAggregate extends BaseAggregateRoot<BaseInvoiceDomainEvent<?>> {


  private InvoiceMain invoiceMain;

  private List<InvoiceDetail> invoiceDetailList;

  private String thirdOrderNo;

  public static InvoiceAggregate build(InvoiceMain main, List<InvoiceDetail> details) {
    InvoiceAggregate aggregate = new InvoiceAggregate();
    aggregate.setInvoiceMain(main);
    aggregate.setInvoiceDetailList(details);
    return aggregate;
  }


  public void rePullEvent(List<RemoteDetail> remoteDetailList) {
    // 添加拉回发票时间
    this.addDomainEvents(new PullBackInvoiceInfoEvent(this,remoteDetailList));
  }

  public boolean isAllowRedInvoice() {
    return this.invoiceMain.getInvoiceStatus().equals(InvoiceStatusEnum.SUCCESS)
        && this.invoiceMain.getInvoiceRedBlueType().equals(InvoiceRedBlueTypeEnum.TAX_INVOICE);
  }

  public void applyRed(RedCreditInvoiceCommand command) {
    String redInvoiceMainNo = SpringUtil.getBean(IdService.class)
        .generateId(IdType.RED_INVOICE_MAIN_NO);
    this.invoiceMain.redApply(redInvoiceMainNo, command);
    for (InvoiceDetail invoiceDetail : this.invoiceDetailList) {
      invoiceDetail.redApply(redInvoiceMainNo);
    }
  }

  public boolean isRedInvoice() {

    return this.invoiceMain.isRedCreditInvoice();
  }


  public OrderExistsInvoiceQueryCommand createExistsInvoiceCommand() {
    OrderExistsInvoiceQueryCommand command = new OrderExistsInvoiceQueryCommand();
    command.setOrderNo(this.invoiceMain.getOrderNo().getOrderNo());
    command.setTransactionChannel(this.invoiceMain.getTransactionChannel());
    command.setPosNo(this.invoiceMain.getPosNo());
    command.setThirdPlatformCode(this.invoiceMain.getThirdPlatformCode());
    command.setThirdOrderNo(this.invoiceMain.getThirdOrderNo());
    command.setStoreCode(this.invoiceMain.getOrganizationCode());
    command.setInvoiceRedBlueType(InvoiceRedBlueTypeEnum.TAX_INVOICE);
    return command;
  }


  public OrderExistsInvoiceQueryCommand createExistsRedInvoiceCommand() {
    OrderExistsInvoiceQueryCommand command = new OrderExistsInvoiceQueryCommand();
    command.setOrderNo(this.invoiceMain.getOrderNo().getOrderNo());
    command.setTransactionChannel(this.invoiceMain.getTransactionChannel());
    command.setPosNo(this.invoiceMain.getPosNo());
    command.setThirdPlatformCode(this.invoiceMain.getThirdPlatformCode());
    command.setThirdOrderNo(this.invoiceMain.getThirdOrderNo());
    command.setStoreCode(this.invoiceMain.getOrganizationCode());
    command.setInvoiceRedBlueType(InvoiceRedBlueTypeEnum.CREDIT_NOTE);
    return command;
  }


  public void invokeApiError(String invoiceCode, InvoiceStatusEnum invoiceStatus,
      String errorMessage, String responseId) {
    this.invoiceMain.setInvoiceStatus(invoiceStatus);
    this.invoiceMain.setInvoiceErrMsg(errorMessage);
    this.invoiceMain.setUpdated(new Date());

    this.invoiceMain.setRemoteInvoiceCode(invoiceCode);
    this.invoiceMain.setRemoteInvoiceResponseId(responseId);
    this.invoiceMain.setVersion(1L);
  }

  public void callBack(CallbackInvoiceDetail callbackInvoiceDetail) {
    InvoiceStatusEnum invoiceStatus = InvoiceStatusEnum.mappingToInvoiceStatus(
        callbackInvoiceDetail.getInvoiceStatus());
    this.invoiceMain.setInvoiceStatus(invoiceStatus);
    if (InvoiceStatusEnum.SUCCESS.equals(invoiceStatus)) {
      this.invoiceMain.setInvoiceErrMsg(Strings.EMPTY);
      this.invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.DONE);
    }
    this.invoiceMain.setPdfUrl(callbackInvoiceDetail.getDownPdf());
    this.invoiceMain.setCompleteTime(callbackInvoiceDetail.getIssueDate());

    this.invoiceMain.setRemoteInvoiceCode(callbackInvoiceDetail.getInvoiceCode());
    this.invoiceMain.setRemoteInvoiceResponseId(callbackInvoiceDetail.getResponseId());
    this.invoiceMain.setSellerTin(callbackInvoiceDetail.getSellerTin());
  }


  public void redCallBack(CallbackRedInvoiceDetail callbackRedInvoiceDetail) {
    InvoiceStatusEnum invoiceStatus = InvoiceStatusEnum.mappingToInvoiceStatus(
        callbackRedInvoiceDetail.getInvoiceStatus());
    this.invoiceMain.setInvoiceStatus(invoiceStatus);
    if (InvoiceStatusEnum.RED_SUCCESS.equals(invoiceStatus)) {
      this.invoiceMain.setInvoiceErrMsg(Strings.EMPTY);
      this.invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.DONE);
    }
    this.invoiceMain.setPdfUrl(callbackRedInvoiceDetail.getDownPdf());
    this.invoiceMain.setCompleteTime(callbackRedInvoiceDetail.getIssueDate());

    this.invoiceMain.setRemoteInvoiceCode(callbackRedInvoiceDetail.getInvoiceCode());
    this.invoiceMain.setRemoteInvoiceResponseId(callbackRedInvoiceDetail.getResponseId());
    this.invoiceMain.setSellerTin(callbackRedInvoiceDetail.getSellerTin());
  }


  public InvoiceAggregate generateRedAggregate() {
    InvoiceMain origin = this.getInvoiceMain();
    List<InvoiceDetail> originDetailList = this.getInvoiceDetailList();

    InvoiceMain redInvoiceMain = BeanUtil.toBean(origin, InvoiceMain.class);
    redInvoiceMain.setRedInvoiceMainNo(origin.getInvoiceMainNo()); // 红冲对应原发票号,红票必填
    redInvoiceMain.setDataSource(InvoiceDataSourceEnum.INVOICE.name());

    List<InvoiceDetail> redInvoiceDetail = originDetailList.stream()
        .map(invoiceDetail -> BeanUtil.toBean(invoiceDetail, InvoiceDetail.class))
        .collect(Collectors.toList());

    InvoiceAggregate redAggregate = build(redInvoiceMain, redInvoiceDetail);
    redAggregate.resetRedInvoice();

    return redAggregate;
  }

  private void resetRedInvoice() {
    this.invoiceMain.resetRedInvoice();
    for (InvoiceDetail invoiceDetail : this.invoiceDetailList) {
      invoiceDetail.resetRedInvoice();
    }
  }
}

