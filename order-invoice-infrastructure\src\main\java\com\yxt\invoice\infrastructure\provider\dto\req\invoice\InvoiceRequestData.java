package com.yxt.invoice.infrastructure.provider.dto.req.invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.invoice.infrastructure.common.utils.StringValidator;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * 发票数据类
 */
@Data
public class InvoiceRequestData {

  /**
   * 外部业务唯一请求号
   */
  @JsonProperty("outRequestCode")
  private String outRequestCode;

  /**
   * 是否含税 Y:是 N:否
   */
  @JsonProperty("inTaxRateTag")
  private String inTaxRateTag;

  /**
   * 发票类型 01：数电专票 02：数电普票
   * @see com.yxt.order.types.invoice.remote.RemoteInvoiceCategory
   */
  @JsonProperty("invoiceType")
  private String invoiceType;

  /**
   * 开票对象：（是否自然人） Y：自然人 N：非自然人（公司）
   */
  @JsonProperty("buyerType")
  private String buyerType;

  /**
   * 购方名称
   */
  @JsonProperty("buyerName")
  private String buyerName;

  /**
   * 购方纳税人识别号\个人身份证号 填充条件: 非自然人必填
   */
  @JsonProperty("buyerTin")
  private String buyerTin;

  /**
   * 购方手机号
   */
  @JsonProperty("buyerMobile")
  private String buyerMobile;

  /**
   * 购方地址 非必填
   */
  @JsonProperty("buyerAddress")
  private String buyerAddress;

  /**
   * 购方电话  非必填
   */
  @JsonProperty("buyerPhone")
  private String buyerPhone;

  /**
   * 开票公司/门店编码
   */
  @JsonProperty("sellerNumber")
  private String sellerNumber;

  /**
   * 显示购方银行帐号 Y：显示 N：不 显示
   */
  @JsonProperty("showBuyerBankAccount")
  private String showBuyerBankAccount;

  /**
   * 购方银行  发票类型为”01”必填
   */
  @JsonProperty("buyerBank")
  private String buyerBank;

  /**
   * 购方银行帐号  发票类型为”01”必填
   */
  @JsonProperty("buyerBankAccount")
  private String buyerBankAccount;

  /**
   * 收款人 非必填
   */
  @JsonProperty("payee")
  private String payee;

  /**
   * 备注 非必填
   */
  @JsonProperty("notes")
  private String notes;

  /**
   * 是否拆票 Y：拆票 N：不拆票 Y
   */
//  @JsonProperty("splitItem") // 和李攀已确定,文档中splitBill标记错误,正确的就是splitItem
//  private String splitItem;
  //李攀让注释掉: 昨天我跟了一下程序splitItem这个字段。这个是拆单的标识，海典他们自己拆好了我这边不再拆单，你们那们也是整单开不拆单开。所以你们用不到的。你看你那边要不要把他删除掉。

  /**
   * 开票请求日期
   * yyyy-MM-dd HH:mm:ss
   */
  @JsonProperty("requestDate")
  private String requestDate;

  /**
   * 开票人 非必填
   */
  @JsonProperty("operator")
  private String operator;

  /**
   * 复核人 非必填
   */
  @JsonProperty("reviewedBy")
  private String reviewedBy;


  /**
   * 特定要素 非必填
   */
  @JsonProperty("specificElements")
  private String specificElements;

  @JsonProperty("itemList")
  private List<InvoiceRequestDataItem> itemList;

  @JsonProperty("xinYunOrder")
  private XinYunOrder xinYunOrder;


  public void valid() {
    Assert.isTrue(!StringUtils.isEmpty(this.buyerName) && StringValidator.isValidString(this.buyerName),"购方名称(除英文名外不允许有空格)");
    Assert.isTrue(Objects.nonNull(this.xinYunOrder),"xinYunOrder不能为空");

    // 税务云让传空,所以这里可以不用校验
//    Assert.isTrue(!StringUtils.isEmpty(this.buyerTin) && this.buyerTin.equals(this.buyerTin.toUpperCase()),"购方纳税人识别号\\个人身份证号（如填写必须全为大写）");
    // 行号必须从1还是编号
    for (InvoiceRequestDataItem invoiceRequestDataItem : itemList) {
      if(!invoiceRequestDataItem.getLine().equals(1)){
        throw new RuntimeException("行号（必须从 1 开始，顺序编号）");
      }
    }
  }
}