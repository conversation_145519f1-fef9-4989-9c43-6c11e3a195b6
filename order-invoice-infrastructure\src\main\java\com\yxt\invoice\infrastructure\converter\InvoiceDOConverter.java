package com.yxt.invoice.infrastructure.converter;

import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceDetailDO;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceMainDO;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import com.yxt.order.types.invoice.remote.RemoteRedInvoiceReason;
import com.yxt.order.types.invoice.remote.RemoteWriteOffState;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * DO与Domain对象转换器 实现防腐层，保护Domain层不受数据库结构变化影响
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class InvoiceDOConverter {

  /**
   * DO转换为Domain对象
   */
  public static InvoiceMain toDomain(InvoiceMainDO invoiceMainDO) {
    if (Objects.isNull(invoiceMainDO)) {
      throw new RuntimeException("查不到发票信息");
    }

    InvoiceMain invoiceMain = new InvoiceMain();
    // 基本字段转换
    invoiceMain.setId(invoiceMainDO.getId());
    invoiceMain.setCompanyCode(invoiceMainDO.getCompanyCode());
    invoiceMain.setCompanyName(invoiceMainDO.getCompanyName());
    invoiceMain.setOrganizationCode(invoiceMainDO.getOrganizationCode());
    invoiceMain.setOrganizationName(invoiceMainDO.getOrganizationName());
    invoiceMain.setInvoiceMainNo(invoiceMainDO.getInvoiceMainNo());
    invoiceMain.setThirdPlatformCode(invoiceMainDO.getThirdPlatformCode());
    invoiceMain.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(invoiceMainDO.getThirdOrderNo()));
    invoiceMain.setOrderNo(OfflineOrderNo.orderNo(invoiceMainDO.getOrderNo()));
    invoiceMain.setPosNo(invoiceMainDO.getPosNo());
    invoiceMain.setUserId(invoiceMainDO.getUserId());
    invoiceMain.setMerCode(invoiceMainDO.getMerCode());
    invoiceMain.setTransactionChannel(invoiceMainDO.getTransactionChannel());
    invoiceMain.setBusinessType(invoiceMainDO.getBusinessType());
    invoiceMain.setRemoteInvoiceCode(invoiceMainDO.getRemoteInvoiceCode());
    invoiceMain.setRemoteInvoiceResponseId(invoiceMainDO.getRemoteInvoiceResponseId());
    invoiceMain.setRemoteWriteOffState(RemoteWriteOffState.getByCode(invoiceMainDO.getRemoteWriteOffState()));
    invoiceMain.setInvoiceRedBlueType(
        InvoiceRedBlueTypeEnum.fromCode(invoiceMainDO.getInvoiceRedBlueType()));
    invoiceMain.setRedInvoiceMainNo(invoiceMainDO.getRedInvoiceMainNo());
    invoiceMain.setRedInvoiceReason(RemoteRedInvoiceReason.getByName(invoiceMainDO.getRedInvoiceReason()));
    invoiceMain.setNotes(invoiceMainDO.getNotes());
    // 枚举类型转换
    invoiceMain.setInvoiceType(InvoiceTypeEnum.fromCode(invoiceMainDO.getInvoiceType()));
    invoiceMain.setInvoiceStatus(InvoiceStatusEnum.fromCode(invoiceMainDO.getInvoiceStatus()));
    invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.fromCode(invoiceMainDO.getSyncStatus()));
    invoiceMain.setBuyerPartyType(
        InvoiceBuyerPartyTypeEnum.fromCode(invoiceMainDO.getBuyerPartyType()));

    // 金额字段
    invoiceMain.setActualPayAmount(invoiceMainDO.getActualPayAmount());
    invoiceMain.setDeliveryAmount(invoiceMainDO.getDeliveryAmount());
    invoiceMain.setInvoiceAmount(invoiceMainDO.getInvoiceAmount());
    invoiceMain.setTaxAmount(invoiceMainDO.getTaxAmount());
    invoiceMain.setPriceTaxAmount(invoiceMainDO.getPriceTaxAmount());

    // 其他字段
    invoiceMain.setDeliveryType(invoiceMainDO.getDeliveryType());
    invoiceMain.setSplitBill(invoiceMainDO.getSplitBill());
    invoiceMain.setOrderCreated(invoiceMainDO.getOrderCreated());
    invoiceMain.setPdfUrl(invoiceMainDO.getPdfUrl());
    invoiceMain.setInvoiceErrMsg(invoiceMainDO.getInvoiceErrMsg());
    invoiceMain.setApplyTime(invoiceMainDO.getApplyTime());
    invoiceMain.setOperator(invoiceMainDO.getOperator());
    invoiceMain.setPayee(invoiceMainDO.getPayee());
    invoiceMain.setReviewed(invoiceMainDO.getReviewed());
    invoiceMain.setApplyChannel(invoiceMainDO.getApplyChannel());

    // 销方信息
    invoiceMain.setSellerNumber(invoiceMainDO.getSellerNumber());
    invoiceMain.setSellerName(invoiceMainDO.getSellerName());
    invoiceMain.setSellerTin(invoiceMainDO.getSellerTin());
    invoiceMain.setSellerAddress(invoiceMainDO.getSellerAddress());
    invoiceMain.setSellerPhone(invoiceMainDO.getSellerPhone());
    invoiceMain.setSellerBank(invoiceMainDO.getSellerBank());
    invoiceMain.setSellerBankAccount(invoiceMainDO.getSellerBankAccount());

    // 购方信息
    invoiceMain.setBuyerName(invoiceMainDO.getBuyerName());
    invoiceMain.setBuyerTin(invoiceMainDO.getBuyerTin());
    invoiceMain.setBuyerAddress(invoiceMainDO.getBuyerAddress());
    invoiceMain.setBuyerPhone(invoiceMainDO.getBuyerPhone());
    invoiceMain.setBuyerBank(invoiceMainDO.getBuyerBank());
    invoiceMain.setBuyerBankAccount(invoiceMainDO.getBuyerBankAccount());
    invoiceMain.setBuyerEmail(invoiceMainDO.getBuyerEmail());
    invoiceMain.setBuyerMobile(invoiceMainDO.getBuyerMobile());
    invoiceMain.setShowBuyerBankAccount(invoiceMainDO.getShowBuyerBankAccount());

    // 系统字段
    invoiceMain.setIsValid(invoiceMainDO.getIsValid());
//    invoiceMain.setProviderParam(invoiceMainDO.getProviderParam());
    invoiceMain.setCreated(invoiceMainDO.getCreated());
    invoiceMain.setUpdated(invoiceMainDO.getUpdated());
    invoiceMain.setCreatedBy(invoiceMainDO.getCreatedBy());
    invoiceMain.setUpdatedBy(invoiceMainDO.getUpdatedBy());
    invoiceMain.setSysCreateTime(invoiceMainDO.getSysCreateTime());
    invoiceMain.setSysUpdateTime(invoiceMainDO.getSysUpdateTime());
    invoiceMain.setVersion(invoiceMainDO.getVersion());
    invoiceMain.setDataSource(invoiceMainDO.getDataSource());

    return invoiceMain;
  }

  /**
   * Domain对象转换为DO
   */
  public static InvoiceMainDO toDO(InvoiceMain invoiceMain) {
    if (invoiceMain == null) {
      return null;
    }

    InvoiceMainDO invoiceMainDO = new InvoiceMainDO();

    // 基本字段转换
    invoiceMainDO.setId(invoiceMain.getId());
    invoiceMainDO.setCompanyCode(invoiceMain.getCompanyCode());
    invoiceMainDO.setCompanyName(invoiceMain.getCompanyName());
    invoiceMainDO.setOrganizationCode(invoiceMain.getOrganizationCode());
    invoiceMainDO.setOrganizationName(invoiceMain.getOrganizationName());
    invoiceMainDO.setInvoiceMainNo(invoiceMain.getInvoiceMainNo());
    invoiceMainDO.setThirdPlatformCode(invoiceMain.getThirdPlatformCode());
    invoiceMainDO.setThirdOrderNo(invoiceMain.getThirdOrderNo().getThirdOrderNo());
    invoiceMainDO.setOrderNo(invoiceMain.getOrderNo().getOrderNo());
    invoiceMainDO.setPosNo(invoiceMain.getPosNo());
    invoiceMainDO.setUserId(invoiceMain.getUserId());
    invoiceMainDO.setMerCode(invoiceMain.getMerCode());
    invoiceMainDO.setTransactionChannel(invoiceMain.getTransactionChannel());
    invoiceMainDO.setBusinessType(invoiceMain.getBusinessType());
    invoiceMainDO.setRemoteInvoiceCode(invoiceMain.getRemoteInvoiceCode());
    invoiceMainDO.setRemoteInvoiceResponseId(invoiceMain.getRemoteInvoiceResponseId());
    if(Objects.nonNull(invoiceMain.getRemoteWriteOffState())){
      invoiceMainDO.setRemoteWriteOffState(invoiceMain.getRemoteWriteOffState().name());
    }
    invoiceMainDO.setInvoiceRedBlueType(invoiceMain.getInvoiceRedBlueType().getCode());
    invoiceMainDO.setRedInvoiceMainNo(invoiceMain.getRedInvoiceMainNo());
    if(Objects.nonNull(invoiceMain.getRedInvoiceReason())){
      invoiceMainDO.setRedInvoiceReason(invoiceMain.getRedInvoiceReason().name());
    }
    invoiceMainDO.setNotes(invoiceMain.getNotes());
    invoiceMainDO.setCompleteTime(invoiceMain.getCompleteTime());
    // 枚举类型转换
    invoiceMainDO.setInvoiceType(invoiceMain.getInvoiceType().getCode());
    invoiceMainDO.setInvoiceStatus(invoiceMain.getInvoiceStatus().getCode());
    invoiceMainDO.setSyncStatus(invoiceMain.getSyncStatus().getCode());
    invoiceMainDO.setBuyerPartyType(Objects.nonNull(invoiceMain.getBuyerPartyType())?invoiceMain.getBuyerPartyType().getCode():null);

    // 金额字段
    invoiceMainDO.setActualPayAmount(invoiceMain.getActualPayAmount());
    invoiceMainDO.setDeliveryAmount(invoiceMain.getDeliveryAmount());
    invoiceMainDO.setInvoiceAmount(invoiceMain.getInvoiceAmount());
    invoiceMainDO.setTaxAmount(invoiceMain.getTaxAmount());
    invoiceMainDO.setPriceTaxAmount(invoiceMain.getPriceTaxAmount());

    // 其他字段
    invoiceMainDO.setDeliveryType(invoiceMain.getDeliveryType());
    invoiceMainDO.setSplitBill(invoiceMain.getSplitBill());
    invoiceMainDO.setOrderCreated(invoiceMain.getOrderCreated());
    invoiceMainDO.setPdfUrl(invoiceMain.getPdfUrl());
    invoiceMainDO.setInvoiceErrMsg(invoiceMain.getInvoiceErrMsg());
    invoiceMainDO.setApplyTime(invoiceMain.getApplyTime());
    invoiceMainDO.setOperator(invoiceMain.getOperator());
    invoiceMainDO.setPayee(invoiceMain.getPayee());
    invoiceMainDO.setReviewed(invoiceMain.getReviewed());
    invoiceMainDO.setApplyChannel(invoiceMain.getApplyChannel());

    // 销方信息
    invoiceMainDO.setSellerNumber(invoiceMain.getSellerNumber());
    invoiceMainDO.setSellerName(invoiceMain.getSellerName());
    invoiceMainDO.setSellerTin(invoiceMain.getSellerTin());
    invoiceMainDO.setSellerAddress(invoiceMain.getSellerAddress());
    invoiceMainDO.setSellerPhone(invoiceMain.getSellerPhone());
    invoiceMainDO.setSellerBank(invoiceMain.getSellerBank());
    invoiceMainDO.setSellerBankAccount(invoiceMain.getSellerBankAccount());

    // 购方信息
    invoiceMainDO.setBuyerName(invoiceMain.getBuyerName());
    invoiceMainDO.setBuyerTin(invoiceMain.getBuyerTin());
    invoiceMainDO.setBuyerAddress(invoiceMain.getBuyerAddress());
    invoiceMainDO.setBuyerPhone(invoiceMain.getBuyerPhone());
    invoiceMainDO.setBuyerBank(invoiceMain.getBuyerBank());
    invoiceMainDO.setBuyerBankAccount(invoiceMain.getBuyerBankAccount());
    invoiceMainDO.setBuyerEmail(invoiceMain.getBuyerEmail());
    invoiceMainDO.setBuyerMobile(invoiceMain.getBuyerMobile());
    invoiceMainDO.setShowBuyerBankAccount(invoiceMain.getShowBuyerBankAccount());

    // 系统字段
    invoiceMainDO.setIsValid(invoiceMain.getIsValid());
    invoiceMainDO.setCreated(invoiceMain.getCreated());
    invoiceMainDO.setUpdated(invoiceMain.getUpdated());
    invoiceMainDO.setCreatedBy(invoiceMain.getCreatedBy());
    invoiceMainDO.setUpdatedBy(invoiceMain.getUpdatedBy());
    invoiceMainDO.setVersion(invoiceMain.getVersion());
    invoiceMainDO.setDataSource(invoiceMain.getDataSource());

    return invoiceMainDO;
  }

  /**
   * InvoiceDetail DO转换为Domain对象
   */
  public static InvoiceDetail toDomain(InvoiceDetailDO invoiceDetailDO) {
    if (Objects.isNull(invoiceDetailDO)) {
      throw new RuntimeException("查不到发票明细");
    }

    InvoiceDetail invoiceDetail = new InvoiceDetail();

    invoiceDetail.setId(invoiceDetailDO.getId());
    invoiceDetail.setInvoiceMainNo(invoiceDetailDO.getInvoiceMainNo());
    invoiceDetail.setInvoiceDetailNo(invoiceDetailDO.getInvoiceDetailNo());
    invoiceDetail.setRowNo(invoiceDetailDO.getRowNo());
    invoiceDetail.setLine(invoiceDetailDO.getLine());
    invoiceDetail.setTaxClassificationCode(invoiceDetailDO.getTaxClassificationCode());
    invoiceDetail.setTopLevelTaxClassificationCode(
        invoiceDetailDO.getTopLevelTaxClassificationCode());
    invoiceDetail.setErpCode(invoiceDetailDO.getErpCode());
    invoiceDetail.setErpName(invoiceDetailDO.getErpName());
    invoiceDetail.setCommodityCount(invoiceDetailDO.getCommodityCount());
    invoiceDetail.setCommoditySpec(invoiceDetailDO.getCommoditySpec());
    invoiceDetail.setUnit(invoiceDetailDO.getUnit());
    invoiceDetail.setPrice(invoiceDetailDO.getPrice());
    invoiceDetail.setTotalAmount(invoiceDetailDO.getTotalAmount());
    invoiceDetail.setTaxAmount(invoiceDetailDO.getTaxAmount());
    invoiceDetail.setTaxRate(invoiceDetailDO.getTaxRate());
    invoiceDetail.setTaxRateCode(invoiceDetailDO.getTaxRateCode());
    invoiceDetail.setPriceTaxAmount(invoiceDetailDO.getPriceTaxAmount());
    invoiceDetail.setInvoiceLineType(invoiceDetailDO.getInvoiceLineType());
    invoiceDetail.setDiscountAmount(invoiceDetailDO.getDiscountAmount());
    invoiceDetail.setPolicyStatus(invoiceDetailDO.getPolicyStatus());
    invoiceDetail.setPolicyTag(invoiceDetailDO.getPolicyTag());
    invoiceDetail.setPolicyTaxRate(invoiceDetailDO.getPolicyTaxRate());
    invoiceDetail.setIsValid(invoiceDetailDO.getIsValid());
    invoiceDetail.setCreated(invoiceDetailDO.getCreated());
    invoiceDetail.setUpdated(invoiceDetailDO.getUpdated());
    invoiceDetail.setCreatedBy(invoiceDetailDO.getCreatedBy());
    invoiceDetail.setUpdatedBy(invoiceDetailDO.getUpdatedBy());
    invoiceDetail.setSysCreateTime(invoiceDetailDO.getSysCreateTime());
    invoiceDetail.setSysUpdateTime(invoiceDetailDO.getSysUpdateTime());
    invoiceDetail.setVersion(invoiceDetailDO.getVersion());

    return invoiceDetail;
  }

  /**
   * InvoiceDetail Domain对象转换为DO
   */
  public static InvoiceDetailDO toDO(InvoiceDetail invoiceDetail) {
    if (Objects.isNull(invoiceDetail)) {
      throw new RuntimeException("invoiceDetail 不能为空");
    }

    InvoiceDetailDO invoiceDetailDO = new InvoiceDetailDO();

    invoiceDetailDO.setId(invoiceDetail.getId());
    invoiceDetailDO.setInvoiceMainNo(invoiceDetail.getInvoiceMainNo());
    invoiceDetailDO.setInvoiceDetailNo(invoiceDetail.getInvoiceDetailNo());
    invoiceDetailDO.setRowNo(invoiceDetail.getRowNo());
    invoiceDetailDO.setLine(invoiceDetail.getLine());
    invoiceDetailDO.setTaxClassificationCode(invoiceDetail.getTaxClassificationCode());
    invoiceDetailDO.setTopLevelTaxClassificationCode(
        invoiceDetail.getTopLevelTaxClassificationCode());
    invoiceDetailDO.setErpCode(invoiceDetail.getErpCode());
    invoiceDetailDO.setErpName(invoiceDetail.getErpName());
    invoiceDetailDO.setCommodityCount(invoiceDetail.getCommodityCount());
    invoiceDetailDO.setCommoditySpec(invoiceDetail.getCommoditySpec());
    invoiceDetailDO.setUnit(invoiceDetail.getUnit());
    invoiceDetailDO.setPrice(invoiceDetail.getPrice());
    invoiceDetailDO.setTotalAmount(invoiceDetail.getTotalAmount());
    invoiceDetailDO.setTaxAmount(invoiceDetail.getTaxAmount());
    invoiceDetailDO.setTaxRate(invoiceDetail.getTaxRate());
    invoiceDetailDO.setTaxRateCode(invoiceDetail.getTaxRateCode());
    invoiceDetailDO.setPriceTaxAmount(invoiceDetail.getPriceTaxAmount());
    invoiceDetailDO.setInvoiceLineType(invoiceDetail.getInvoiceLineType());
    invoiceDetailDO.setDiscountAmount(invoiceDetail.getDiscountAmount());
    invoiceDetailDO.setPolicyStatus(invoiceDetail.getPolicyStatus());
    invoiceDetailDO.setPolicyTag(invoiceDetail.getPolicyTag());
    invoiceDetailDO.setPolicyTaxRate(invoiceDetail.getPolicyTaxRate());
    invoiceDetailDO.setIsValid(invoiceDetail.getIsValid());
    invoiceDetailDO.setCreated(invoiceDetail.getCreated());
    invoiceDetailDO.setUpdated(invoiceDetail.getUpdated());
    invoiceDetailDO.setCreatedBy(invoiceDetail.getCreatedBy());
    invoiceDetailDO.setUpdatedBy(invoiceDetail.getUpdatedBy());
    invoiceDetailDO.setSysCreateTime(invoiceDetail.getSysCreateTime());
    invoiceDetailDO.setSysUpdateTime(invoiceDetail.getSysUpdateTime());
    invoiceDetailDO.setVersion(invoiceDetail.getVersion());

    return invoiceDetailDO;
  }



  /**
   * 批量转换InvoiceDetail DO列表为Domain列表
   */
  public static List<InvoiceDetail> toDetailDomainList(List<InvoiceDetailDO> invoiceDetailDOList) {
    if (invoiceDetailDOList == null) {
      return null;
    }
    return invoiceDetailDOList.stream()
        .map(InvoiceDOConverter::toDomain)
        .collect(Collectors.toList());
  }

}
