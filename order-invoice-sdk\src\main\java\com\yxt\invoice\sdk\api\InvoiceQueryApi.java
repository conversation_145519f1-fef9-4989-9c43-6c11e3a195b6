package com.yxt.invoice.sdk.api;

import com.yxt.invoice.sdk.dto.req.QueryOrderExistsInvoiceReqDto;
import com.yxt.invoice.sdk.dto.req.QueryThirdOrderExistsInvoiceReqDto;
import com.yxt.invoice.sdk.dto.res.QueryOrderExistsInvoiceResDto;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 发票服务SDK接口 为医药电商平台提供开票相关服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceQueryApi {

  /**
   * 查询订单是否开具发票
   *
   * @param req 发票ID
   * @return 发票详情结果
   */
  @PostMapping("/queryOrderExistsInvoice")
  @ApiOperation(value = "查询订单是否开具发票")
  ResponseBase<QueryOrderExistsInvoiceResDto> queryOrderExistsInvoice(
      @Valid @RequestBody QueryOrderExistsInvoiceReqDto req);


  /**
   * 查询订单是否开具发票-根据三方订单信息
   *
   * @param req 发票ID
   * @return 发票详情结果
   */
  @PostMapping("/queryThirdOrderExistsInvoiceReqDto")
  @ApiOperation(value = "查询订单是否开具发票-根据三方订单信息")
  ResponseBase<QueryOrderExistsInvoiceResDto> queryThirdOrderExistsInvoiceReqDto(
      @Valid @RequestBody QueryThirdOrderExistsInvoiceReqDto req);

}
