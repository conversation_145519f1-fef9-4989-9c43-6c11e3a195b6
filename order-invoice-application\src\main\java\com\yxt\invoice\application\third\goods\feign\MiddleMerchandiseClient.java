package com.yxt.invoice.application.third.goods.feign;

import com.yxt.invoice.application.third.goods.dto.req.AveragePriceQuery;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 商品中台Client
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/17 18:51
 */
@FeignClient(value = "hydee-middle-merchandise")
public interface MiddleMerchandiseClient {

    /**
     * 查询商品库存-新接口
     */
    @PostMapping("/1.0/ds/queryAveragePrice")
    ResponseBase<List<AveragePriceVO>> queryAveragePrice(@RequestHeader("userId") String userId,
                                                         @RequestBody AveragePriceQuery averagePriceQuery);

}
