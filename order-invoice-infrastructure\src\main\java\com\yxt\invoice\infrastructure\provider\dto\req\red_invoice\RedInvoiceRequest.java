package com.yxt.invoice.infrastructure.provider.dto.req.red_invoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.invoice.infrastructure.provider.dto.req.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 红票请求主类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RedInvoiceRequest extends BaseReq {

  @JsonProperty("data")
  private RedInvoiceRequestData data;


  public void valid() {
    data.valid();
  }
}
