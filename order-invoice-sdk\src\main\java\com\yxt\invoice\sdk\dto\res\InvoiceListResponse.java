package com.yxt.invoice.sdk.dto.res;

import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.lang.dto.api.ResponseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发票列表响应
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class InvoiceListResponse extends ResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发票列表
     */
    @ApiModelProperty(value = "发票列表")
    private List<InvoiceMainDTO> list;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数", example = "100")
    private Long total;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer pageNum;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer pageSize;

    /**
     * 总页数
     */
    @ApiModelProperty(value = "总页数", example = "5")
    private Integer totalPages;


}
